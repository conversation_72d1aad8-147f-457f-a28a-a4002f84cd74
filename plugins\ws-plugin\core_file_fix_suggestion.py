# 建议的Core文件处理修复方案
# 文件位置: /root/gsuid_core/gsuid_core/plugins/WutheringWavesUID/WutheringWavesUID/wutheringwaves_gachalog/get_gachalogs.py

import base64
import json
import aiohttp
import asyncio

async def import_gachalogs_fixed(ev, history_url, type, uid):
    """
    修复后的文件处理逻辑，支持URL和base64两种格式
    """
    try:
        if type == 'url':
            # 处理URL格式 - 自行下载文件
            print(f"检测到URL格式，开始下载文件: {history_url}")
            
            async with aiohttp.ClientSession() as session:
                async with session.get(history_url) as response:
                    if response.status == 200:
                        data_bytes = await response.read()
                        print(f"文件下载成功，大小: {len(data_bytes)} bytes")
                    else:
                        return f"文件下载失败，HTTP状态码: {response.status}"
        else:
            # 处理base64格式 - 原有逻辑
            print(f"检测到base64格式，开始解码")
            data_bytes = base64.b64decode(history_url)
        
        # 统一的JSON解析逻辑
        try:
            history_data = json.loads(data_bytes.decode('utf-8'))
        except UnicodeDecodeError:
            try:
                history_data = json.loads(data_bytes.decode('gbk'))
            except UnicodeDecodeError:
                return "文件编码不支持，请确保文件为UTF-8或GBK编码"
        except json.JSONDecodeError:
            return "请传入正确的JSON格式文件!"
        
        # 后续处理逻辑...
        print(f"JSON解析成功，数据条数: {len(history_data) if isinstance(history_data, list) else '未知'}")
        return "文件处理成功"
        
    except Exception as e:
        print(f"文件处理异常: {str(e)}")
        return f"文件处理失败: {str(e)}"

# 修改建议：
# 1. 在get_gachalogs.py的import_gachalogs函数中
# 2. 在第302-310行左右，将原有的逻辑替换为上述逻辑
# 3. 需要添加aiohttp依赖来处理HTTP请求

# 原有代码（有问题的部分）：
"""
else:
    data_bytes = base64.b64decode(history_url)  # 这里会出错，因为history_url是URL不是base64
    try:
        history_data = json.loads(data_bytes.decode())
    except UnicodeDecodeError:
        history_data = json.loads(data_bytes.decode("gbk"))
    except json.decoder.JSONDecodeError:
        return "请传入正确的JSON格式文件!"
"""

# 修改后的代码：
"""
else:
    if type == 'url':
        # URL格式 - 下载文件
        async with aiohttp.ClientSession() as session:
            async with session.get(history_url) as response:
                if response.status == 200:
                    data_bytes = await response.read()
                else:
                    return f"文件下载失败，HTTP状态码: {response.status}"
    else:
        # base64格式 - 解码
        data_bytes = base64.b64decode(history_url)
    
    try:
        history_data = json.loads(data_bytes.decode())
    except UnicodeDecodeError:
        history_data = json.loads(data_bytes.decode("gbk"))
    except json.decoder.JSONDecodeError:
        return "请传入正确的JSON格式文件!"
"""
