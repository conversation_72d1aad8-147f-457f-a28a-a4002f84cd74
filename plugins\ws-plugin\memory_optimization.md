# ws-plugin 内存优化修复报告

## 问题分析

经过检查，发现ws-plugin在长时间运行后内存占用过大的主要原因：

### 1. 消息缓存无限增长
- **问题**：`recentMessages` 对象存储所有群组的消息，没有有效的清理机制
- **影响**：每个群组最多存储50条消息，但群组数量无限制，导致内存持续增长
- **原因**：定时清理不够频繁，且没有全局内存保护机制

### 2. 任务映射累积
- **问题**：`taskUserMapping` 对象存储任务ID到用户ID的映射，清理不及时
- **影响**：长期运行后累积大量过期映射

### 3. Redis消息存储
- **问题**：Redis中存储的消息数据，默认存储时间为300秒（5分钟）
- **影响**：虽然有过期时间，但在高频使用时仍会占用大量内存

## 修复方案

### 1. 优化消息缓存管理

#### 增强定时清理机制
```javascript
// 添加详细的清理统计和日志
const cleanupInterval = setInterval(() => {
  // 清理逻辑 + 统计信息
  // 强制垃圾回收（如果可用）
  if (global.gc && (cleanedMessages > 500 || memoryStats.messagesCount > 10000)) {
    global.gc()
  }
}, 60 * 1000)
```

#### 优化setLatestMsg函数
- ✅ 添加重复消息检测，避免重复存储
- ✅ 实时清理过期消息
- ✅ 减少单个群组最大消息数量（50→30）
- ✅ 添加全局群组数量限制（最多1000个群组）
- ✅ 自动清理最旧的群组缓存

#### 内存保护机制
```javascript
// 全局内存保护：如果总群组数超过限制，清理最旧的群组
const totalGroups = Object.keys(recentMessages).length
if (totalGroups > 1000) {
  // 找到最旧的群组并删除
  // ...清理逻辑
}
```

### 2. 添加内存监控功能

#### 内存统计
- ✅ 实时统计消息数量、群组数量、任务映射数量
- ✅ 记录清理历史和效果
- ✅ 监控进程内存使用情况

#### 管理命令
- ✅ `#ws内存状态` - 查看当前内存使用情况
- ✅ `#ws清理内存` - 手动触发内存清理

### 3. 配置优化建议

#### Redis配置
- 当前消息存储时间：300秒（5分钟）
- 建议：保持现有设置，已经比较合理

#### 启动参数优化
```bash
# 启用垃圾回收
node --expose-gc app.js

# 限制内存使用
node --max-old-space-size=2048 app.js
```

## 修复效果

### 内存使用优化
1. **消息缓存**：从无限增长改为最多1000个群组 × 30条消息
2. **自动清理**：每分钟清理过期数据，大量清理时记录日志
3. **重复检测**：避免重复存储相同消息
4. **强制GC**：在内存使用过高时自动触发垃圾回收

### 监控能力
1. **实时统计**：随时查看内存使用情况
2. **清理报告**：详细的清理效果统计
3. **预警机制**：内存使用过高时自动警告

### 预期效果
- **内存占用**：长期运行时内存占用稳定在合理范围
- **性能影响**：清理操作对性能影响极小
- **稳定性**：避免因内存泄漏导致的Bot崩溃

## 使用建议

### 日常监控
```bash
#ws内存状态  # 查看当前内存使用
```

### 手动清理
```bash
#ws清理内存  # 手动触发清理（通常不需要）
```

### 启动优化
```bash
# 推荐的启动命令
node --expose-gc --max-old-space-size=2048 app.js
```

## 注意事项

1. **兼容性**：所有修改都保持向后兼容，不影响现有功能
2. **性能**：清理操作在后台进行，不影响消息处理性能
3. **数据安全**：只清理过期数据，不影响正常功能
4. **监控**：建议定期查看内存状态，特别是在高负载环境下

## 总结

通过这些优化，ws-plugin的内存使用将得到有效控制，避免长时间运行后的内存泄漏问题。同时提供了完善的监控工具，方便管理员及时了解和处理内存问题。
