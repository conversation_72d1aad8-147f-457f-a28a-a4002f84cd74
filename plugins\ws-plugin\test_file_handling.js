// 测试文件处理功能的简单脚本
// 这个文件用于验证ws-plugin的文件处理修复是否正确工作

import { makeGSUidReportMsg, makeGSUidSendMsg } from './model/makeMsg.js'

// 模拟文件消息事件
const mockFileEvent = {
  message: [
    {
      type: 'file',
      name: 'test.txt',
      url: 'https://example.com/test.txt'
    }
  ],
  isGroup: true,
  group_id: '123456',
  user_id: '789012',
  self_id: '345678',
  message_id: 'msg_001',
  time: Date.now(),
  sender: {
    nickname: 'TestUser',
    role: 'member'
  },
  file: {
    fid: 'file_123',
    name: 'test.txt'
  },
  group: {
    getFileUrl: async (fid) => {
      // 模拟获取文件URL
      return 'https://example.com/download/test.txt'
    }
  }
}

// 模拟gsuid_core发送文件消息
const mockGSUidSendData = {
  bot_id: 'onebot',
  bot_self_id: '345678',
  target_type: 'group',
  target_id: '123456',
  content: [
    {
      type: 'file',
      data: 'test.txt|dGVzdCBmaWxlIGNvbnRlbnQ=' // base64编码的"test file content"
    }
  ]
}

// 测试函数
async function testFileHandling() {
  console.log('开始测试文件处理功能...')
  
  try {
    // 测试接收文件并转换为gsuid_core格式
    console.log('测试1: 接收文件消息并转换为gsuid_core格式')
    const reportMsg = await makeGSUidReportMsg(mockFileEvent, 'onebot')
    console.log('转换结果:', JSON.parse(reportMsg.toString()))
    
    // 测试从gsuid_core接收文件并转换为发送格式
    console.log('\n测试2: 从gsuid_core接收文件并转换为发送格式')
    const { sendMsg, quote } = await makeGSUidSendMsg(mockGSUidSendData)
    console.log('发送消息:', sendMsg)
    console.log('引用消息:', quote)
    
    console.log('\n文件处理功能测试完成!')
  } catch (error) {
    console.error('测试过程中出现错误:', error)
  }
}

// 如果直接运行此文件，执行测试
if (import.meta.url === `file://${process.argv[1]}`) {
  testFileHandling()
}

export { testFileHandling, mockFileEvent, mockGSUidSendData }
