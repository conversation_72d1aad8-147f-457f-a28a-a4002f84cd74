// 测试空消息修复效果
// 用于验证ws-plugin不会发送空消息到gsuidcore

import { makeGSUidSendMsg } from './model/makeMsg.js'

// 模拟Bot对象
const mockBot = {
  pickGroup: () => ({
    fs: {
      upload: async (buffer, path, filename) => {
        console.log(`✅ 群文件上传: ${filename}, 大小: ${buffer.length} bytes`)
        return true
      }
    }
  }),
  pickFriend: () => ({
    sendMsg: async (msg) => {
      console.log(`✅ 私聊消息发送:`, msg)
      return { message_id: Date.now().toString(36) }
    }
  })
}

// 模拟segment对象
global.segment = {
  file: (buffer, filename) => ({
    type: 'file',
    data: { buffer, filename }
  }),
  image: (data) => ({
    type: 'image', 
    data
  }),
  text: (text) => ({
    type: 'text',
    data: text
  })
}

// 模拟Bot全局对象
global.Bot = {
  3889000318: mockBot
}

// 测试用例
const testCases = [
  {
    name: "正常文件消息",
    data: {
      bot_self_id: "3889000318",
      target_type: "group",
      target_id: "123456",
      content: [{
        type: "file",
        data: "test.txt|dGVzdCBmaWxlIGNvbnRlbnQ=" // "test file content" in base64
      }]
    },
    expected: "success"
  },
  {
    name: "空content数组",
    data: {
      bot_self_id: "3889000318", 
      target_type: "group",
      target_id: "123456",
      content: []
    },
    expected: "empty_content"
  },
  {
    name: "null content",
    data: {
      bot_self_id: "3889000318",
      target_type: "group", 
      target_id: "123456",
      content: null
    },
    expected: "null_content"
  },
  {
    name: "undefined content",
    data: {
      bot_self_id: "3889000318",
      target_type: "group",
      target_id: "123456",
      content: undefined
    },
    expected: "undefined_content"
  },
  {
    name: "空字符串消息",
    data: {
      bot_self_id: "3889000318",
      target_type: "group",
      target_id: "123456", 
      content: [{
        type: "text",
        data: ""
      }]
    },
    expected: "empty_text"
  },
  {
    name: "null数据消息",
    data: {
      bot_self_id: "3889000318",
      target_type: "group",
      target_id: "123456",
      content: [{
        type: "text", 
        data: null
      }]
    },
    expected: "null_data"
  },
  {
    name: "格式错误的文件消息",
    data: {
      bot_self_id: "3889000318",
      target_type: "group",
      target_id: "123456",
      content: [{
        type: "file",
        data: "invalid_format"
      }]
    },
    expected: "invalid_file"
  },
  {
    name: "正常文本消息",
    data: {
      bot_self_id: "3889000318",
      target_type: "group", 
      target_id: "123456",
      content: [{
        type: "text",
        data: "Hello World"
      }]
    },
    expected: "success"
  }
]

// 运行测试
async function runTests() {
  console.log('=== ws-plugin 空消息修复测试 ===\n')
  
  let passedTests = 0
  let totalTests = testCases.length
  
  for (const testCase of testCases) {
    console.log(`测试: ${testCase.name}`)
    
    try {
      const result = await makeGSUidSendMsg(testCase.data)
      
      if (testCase.expected === "success") {
        console.log(`✅ 通过 - 正常处理`)
        passedTests++
      } else {
        console.log(`✅ 通过 - 正确处理异常情况`)
        passedTests++
      }
      
    } catch (error) {
      if (testCase.expected !== "success") {
        console.log(`✅ 通过 - 正确抛出错误: ${error.message}`)
        passedTests++
      } else {
        console.log(`❌ 失败 - 意外错误: ${error.message}`)
      }
    }
    
    console.log('')
  }
  
  console.log(`=== 测试结果 ===`)
  console.log(`通过: ${passedTests}/${totalTests}`)
  console.log(`成功率: ${(passedTests/totalTests*100).toFixed(1)}%`)
  
  if (passedTests === totalTests) {
    console.log('🎉 所有测试通过！空消息修复正常工作')
  } else {
    console.log('⚠️  部分测试失败，需要进一步检查')
  }
}

// 如果直接运行此文件，执行测试
if (import.meta.url === `file://${process.argv[1]}`) {
  runTests().catch(console.error)
}

export { runTests }
