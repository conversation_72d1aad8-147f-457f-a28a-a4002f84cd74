import plugin from '../../../lib/plugins/plugin.js'
import { getMemoryStats, manualCleanup } from '../model/DataBase.js'

export class wsMemory extends plugin {
  constructor() {
    super({
      name: 'ws-plugin内存管理',
      dsc: 'ws-plugin内存使用监控和清理',
      event: 'message',
      priority: 1000,
      rule: [
        {
          reg: '^#ws内存状态$',
          fnc: 'memoryStatus'
        },
        {
          reg: '^#ws清理内存$',
          fnc: 'cleanMemory'
        }
      ]
    })
  }

  async memoryStatus() {
    if (!this.e.isMaster) {
      return this.reply('只有主人才能查看内存状态')
    }

    try {
      const stats = getMemoryStats()
      const process_memory = process.memoryUsage()
      
      let msg = [
        '=== ws-plugin 内存状态 ===',
        `消息缓存: ${stats.messagesCount} 条`,
        `群组缓存: ${stats.groupsCount} 个`,
        `任务映射: ${stats.taskMappingsCount} 个`,
        `上次清理: ${new Date(stats.lastCleanup).toLocaleString()}`,
        `距离上次清理: ${Math.floor(stats.timeSinceLastCleanup / 1000)} 秒`,
        '',
        '=== 进程内存使用 ===',
        `RSS: ${Math.round(process_memory.rss / 1024 / 1024)} MB`,
        `堆内存: ${Math.round(process_memory.heapUsed / 1024 / 1024)} MB`,
        `堆总量: ${Math.round(process_memory.heapTotal / 1024 / 1024)} MB`,
        `外部内存: ${Math.round(process_memory.external / 1024 / 1024)} MB`
      ]

      // 内存使用警告
      if (stats.messagesCount > 5000) {
        msg.push('', '⚠️ 警告: 消息缓存过多，建议清理内存')
      }
      if (stats.groupsCount > 500) {
        msg.push('', '⚠️ 警告: 群组缓存过多，建议清理内存')
      }
      if (process_memory.heapUsed > 500 * 1024 * 1024) {
        msg.push('', '⚠️ 警告: 堆内存使用过高，建议重启Bot')
      }

      return this.reply(msg.join('\n'))
    } catch (error) {
      logger.error('[ws-plugin] 获取内存状态失败:', error)
      return this.reply('获取内存状态失败')
    }
  }

  async cleanMemory() {
    if (!this.e.isMaster) {
      return this.reply('只有主人才能清理内存')
    }

    try {
      const beforeStats = getMemoryStats()
      const cleanResult = manualCleanup()
      const afterStats = getMemoryStats()

      let msg = [
        '=== 内存清理完成 ===',
        `清理消息: ${cleanResult.cleanedMessages} 条`,
        `清理群组: ${cleanResult.cleanedGroups} 个`,
        `清理任务映射: ${cleanResult.cleanedTasks} 个`,
        '',
        '=== 清理前后对比 ===',
        `消息: ${beforeStats.messagesCount} → ${afterStats.messagesCount}`,
        `群组: ${beforeStats.groupsCount} → ${afterStats.groupsCount}`,
        `任务映射: ${beforeStats.taskMappingsCount} → ${afterStats.taskMappingsCount}`
      ]

      // 如果启用了垃圾回收
      if (global.gc) {
        msg.push('', '✅ 已执行强制垃圾回收')
      } else {
        msg.push('', '💡 提示: 启动时添加 --expose-gc 参数可启用强制垃圾回收')
      }

      return this.reply(msg.join('\n'))
    } catch (error) {
      logger.error('[ws-plugin] 清理内存失败:', error)
      return this.reply('清理内存失败')
    }
  }
}
