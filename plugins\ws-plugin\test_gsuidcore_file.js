// 测试文件是否能正确发送到gsuidcore

// 模拟全局变量
global.Bot = {}
global.logger = {
  info: console.log,
  warn: console.warn,
  error: console.error,
  mark: console.log
}

global.segment = {
  file: (buffer, name) => ({ type: 'file', buffer, name })
}

global.Version = { isTrss: false }

// 测试文件上报到gsuidcore
async function testFileReportToGSUidCore() {
  console.log('=== 测试文件上报到gsuidcore ===')
  
  // 模拟fetch
  global.fetch = async (url) => {
    console.log(`  下载文件: ${url}`)
    const testData = JSON.stringify({ test: 'file_data' })
    return {
      arrayBuffer: async () => new TextEncoder().encode(testData).buffer
    }
  }
  
  // 模拟makeGSUidReportMsg函数的文件处理部分
  async function simulateFileReport(e, i) {
    const message = []
    
    // 模拟文件处理逻辑
    try {
      let fileUrl
      if (e.isGroup) {
        // 群文件处理
        fileUrl = await e.group?.getFileUrl?.(e.file.fid)
      } else {
        // 私聊文件处理
        fileUrl = await e.friend?.getFileUrl?.(e.file.fid)
      }
      
      if (fileUrl) {
        let res = await fetch(fileUrl)
        let arrayBuffer = await res.arrayBuffer()
        let buffer = Buffer.from(arrayBuffer)
        let base64 = buffer.toString("base64")
        let name = i.name
        message.push({
          type: "file",
          data: `${name}|${base64}`,
        })
        console.log(`  ✓ 文件处理成功: ${name}, base64长度: ${base64.length}`)
        console.log(`  文件数据: ${message[0].data.substring(0, 50)}...`)
      } else {
        console.log(`  ✗ 无法获取文件URL`)
      }
    } catch (error) {
      console.log(`  ✗ 文件处理失败: ${error.message}`)
    }
    
    return message
  }
  
  // 测试1: 私聊文件上报
  console.log('\n1. 测试私聊文件上报:')
  const privateFileEvent = {
    isGroup: false,
    file: { fid: 'private123' },
    friend: {
      getFileUrl: async (fid) => {
        console.log(`  获取私聊文件URL: ${fid}`)
        return 'http://example.com/private_file.json'
      }
    }
  }
  
  const privateFileItem = { name: 'private_file.json' }
  const privateResult = await simulateFileReport(privateFileEvent, privateFileItem)
  
  if (privateResult.length > 0) {
    console.log(`  ✅ 私聊文件将发送到gsuidcore: ${JSON.stringify(privateResult[0])}`)
  } else {
    console.log(`  ❌ 私聊文件未能生成上报消息`)
  }
  
  // 测试2: 群聊文件上报
  console.log('\n2. 测试群聊文件上报:')
  const groupFileEvent = {
    isGroup: true,
    file: { fid: 'group123' },
    group: {
      getFileUrl: async (fid) => {
        console.log(`  获取群文件URL: ${fid}`)
        return 'http://example.com/group_file.json'
      }
    }
  }
  
  const groupFileItem = { name: 'group_file.json' }
  const groupResult = await simulateFileReport(groupFileEvent, groupFileItem)
  
  if (groupResult.length > 0) {
    console.log(`  ✅ 群聊文件将发送到gsuidcore: ${JSON.stringify(groupResult[0])}`)
  } else {
    console.log(`  ❌ 群聊文件未能生成上报消息`)
  }
  
  // 测试3: TRSS环境下的文件上报
  console.log('\n3. 测试TRSS环境下的文件上报:')
  global.Version = { isTrss: true }
  
  const trssFileEvent = {
    isGroup: false,
    file: { fid: 'trss123' },
    friend: {
      getFileUrl: async (fid) => {
        console.log(`  TRSS获取文件URL: ${fid}`)
        return 'http://example.com/trss_file.json'
      }
    }
  }
  
  const trssFileItem = { name: 'trss_file.json' }
  const trssResult = await simulateFileReport(trssFileEvent, trssFileItem)
  
  if (trssResult.length > 0) {
    console.log(`  ✅ TRSS文件将发送到gsuidcore: ${JSON.stringify(trssResult[0])}`)
  } else {
    console.log(`  ❌ TRSS文件未能生成上报消息`)
  }
}

// 测试完整的消息上报流程
async function testCompleteMessageReport() {
  console.log('\n=== 测试完整消息上报流程 ===')
  
  // 模拟完整的makeGSUidReportMsg函数
  async function makeGSUidReportMsg(e, botId = 'onebot') {
    let message = []
    let msg = e.message
    
    // 处理source
    if (e.source) {
      message.push({
        type: 'reply',
        data: String(e.source.message_id)
      })
    }
    
    // 处理消息内容
    for (const i of msg) {
      switch (i.type) {
        case 'text':
          message.push({
            type: 'text',
            data: i.text
          })
          break
        case 'file':
          // 文件处理逻辑
          try {
            let fileUrl
            if (e.isGroup) {
              fileUrl = await e.group?.getFileUrl?.(e.file.fid)
            } else {
              fileUrl = await e.friend?.getFileUrl?.(e.file.fid)
            }
            
            if (fileUrl) {
              let res = await fetch(fileUrl)
              let arrayBuffer = await res.arrayBuffer()
              let buffer = Buffer.from(arrayBuffer)
              let base64 = buffer.toString("base64")
              let name = i.name
              message.push({
                type: "file",
                data: `${name}|${base64}`,
              })
              console.log(`  ✓ 文件添加到消息: ${name}`)
            }
          } catch (error) {
            console.log(`  ✗ 文件处理失败: ${error.message}`)
          }
          break
      }
    }
    
    if (message.length == 0) {
      return false
    }
    
    // 构建完整的上报消息
    const MessageReceive = {
      bot_id: botId,
      bot_self_id: String(e.self_id),
      msg_id: String(e.message_id),
      user_id: String(e.user_id),
      user_pm: 6,
      content: message,
      sender: {
        ...e.sender,
        user_id: String(e.user_id)
      }
    }
    
    if (e.isGroup) {
      MessageReceive.user_type = 'group'
      MessageReceive.group_id = String(e.group_id)
    } else {
      MessageReceive.user_type = 'direct'
    }
    
    return Buffer.from(JSON.stringify(MessageReceive))
  }
  
  // 测试包含文件的消息
  console.log('\n测试包含文件的完整消息:')
  const testEvent = {
    message: [
      { type: 'text', text: '这是一个文件:' },
      { type: 'file', name: 'test_document.pdf' }
    ],
    isGroup: false,
    file: { fid: 'test_fid_123' },
    friend: {
      getFileUrl: async (fid) => {
        console.log(`  获取文件URL: ${fid}`)
        return 'http://example.com/test_document.pdf'
      }
    },
    self_id: 12345,
    message_id: 67890,
    user_id: 11111,
    sender: { nickname: 'TestUser' }
  }
  
  const reportResult = await makeGSUidReportMsg(testEvent)
  
  if (reportResult) {
    const parsedResult = JSON.parse(reportResult.toString())
    console.log(`  ✅ 完整消息上报成功`)
    console.log(`  消息内容数量: ${parsedResult.content.length}`)
    console.log(`  包含的消息类型: ${parsedResult.content.map(c => c.type).join(', ')}`)
    
    // 检查是否包含文件
    const fileMsg = parsedResult.content.find(c => c.type === 'file')
    if (fileMsg) {
      console.log(`  ✅ 文件消息存在: ${fileMsg.data.substring(0, 50)}...`)
    } else {
      console.log(`  ❌ 文件消息缺失`)
    }
  } else {
    console.log(`  ❌ 消息上报失败`)
  }
}

// 运行所有测试
async function runAllTests() {
  await testFileReportToGSUidCore()
  await testCompleteMessageReport()
  
  console.log('\n=== 测试总结 ===')
  console.log('如果以上测试都显示 ✅，说明文件能正确发送到gsuidcore')
  console.log('如果显示 ❌，说明文件处理逻辑有问题')
}

runAllTests().catch(console.error)
