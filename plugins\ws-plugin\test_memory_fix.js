// 内存优化测试脚本
// 用于验证ws-plugin内存优化修复的效果

import { setLatestMsg, getMemoryStats, manualCleanup } from './model/DataBase.js'

// 模拟大量消息数据来测试内存管理
function simulateMessages() {
  console.log('开始模拟大量消息数据...')
  
  const startTime = Date.now()
  const baseTime = Math.floor(startTime / 1000)
  
  // 模拟1000个群组，每个群组50条消息
  for (let groupId = 1; groupId <= 1000; groupId++) {
    for (let msgIndex = 1; msgIndex <= 50; msgIndex++) {
      const messageData = {
        time: baseTime - (50 - msgIndex) * 10, // 模拟时间间隔
        message_id: `msg_${groupId}_${msgIndex}`,
        user_id: `user_${(msgIndex % 10) + 1}`, // 每个群10个用户
        reply: () => console.log(`回复消息 ${groupId}_${msgIndex}`)
      }
      
      setLatestMsg(`group_${groupId}`, messageData)
    }
  }
  
  console.log(`模拟完成，耗时: ${Date.now() - startTime}ms`)
}

// 测试内存统计功能
function testMemoryStats() {
  console.log('\n=== 内存统计测试 ===')
  const stats = getMemoryStats()
  
  console.log(`消息数量: ${stats.messagesCount}`)
  console.log(`群组数量: ${stats.groupsCount}`)
  console.log(`任务映射数量: ${stats.taskMappingsCount}`)
  console.log(`上次清理时间: ${new Date(stats.lastCleanup).toLocaleString()}`)
  
  return stats
}

// 测试内存清理功能
function testMemoryCleanup() {
  console.log('\n=== 内存清理测试 ===')
  
  const beforeStats = getMemoryStats()
  console.log('清理前:')
  console.log(`  消息: ${beforeStats.messagesCount}`)
  console.log(`  群组: ${beforeStats.groupsCount}`)
  
  const cleanResult = manualCleanup()
  console.log('清理结果:')
  console.log(`  清理消息: ${cleanResult.cleanedMessages}`)
  console.log(`  清理群组: ${cleanResult.cleanedGroups}`)
  console.log(`  清理任务映射: ${cleanResult.cleanedTasks}`)
  
  const afterStats = getMemoryStats()
  console.log('清理后:')
  console.log(`  消息: ${afterStats.messagesCount}`)
  console.log(`  群组: ${afterStats.groupsCount}`)
  
  return { beforeStats, afterStats, cleanResult }
}

// 测试重复消息处理
function testDuplicateMessages() {
  console.log('\n=== 重复消息测试 ===')
  
  const testGroupId = 'test_group_duplicate'
  const messageData = {
    time: Math.floor(Date.now() / 1000),
    message_id: 'duplicate_msg_001',
    user_id: 'test_user_001',
    reply: () => console.log('回复重复消息')
  }
  
  // 添加相同消息多次
  for (let i = 0; i < 5; i++) {
    setLatestMsg(testGroupId, messageData)
  }
  
  const stats = getMemoryStats()
  console.log(`重复消息测试完成，当前消息总数: ${stats.messagesCount}`)
  
  return stats
}

// 测试过期消息处理
function testExpiredMessages() {
  console.log('\n=== 过期消息测试 ===')
  
  const testGroupId = 'test_group_expired'
  const now = Math.floor(Date.now() / 1000)
  
  // 添加一些过期消息（6分钟前）
  for (let i = 1; i <= 10; i++) {
    const expiredMessageData = {
      time: now - 6 * 60, // 6分钟前
      message_id: `expired_msg_${i}`,
      user_id: `expired_user_${i}`,
      reply: () => console.log(`回复过期消息 ${i}`)
    }
    setLatestMsg(testGroupId, expiredMessageData)
  }
  
  // 添加一些新消息
  for (let i = 1; i <= 5; i++) {
    const newMessageData = {
      time: now - 60, // 1分钟前
      message_id: `new_msg_${i}`,
      user_id: `new_user_${i}`,
      reply: () => console.log(`回复新消息 ${i}`)
    }
    setLatestMsg(testGroupId, newMessageData)
  }
  
  const beforeStats = getMemoryStats()
  console.log(`添加过期消息后，消息总数: ${beforeStats.messagesCount}`)
  
  // 触发清理
  const cleanResult = manualCleanup()
  const afterStats = getMemoryStats()
  
  console.log(`清理过期消息后，消息总数: ${afterStats.messagesCount}`)
  console.log(`清理的消息数: ${cleanResult.cleanedMessages}`)
  
  return { beforeStats, afterStats, cleanResult }
}

// 主测试函数
async function runTests() {
  console.log('=== ws-plugin 内存优化测试 ===\n')
  
  try {
    // 1. 测试初始状态
    console.log('1. 初始内存状态:')
    testMemoryStats()
    
    // 2. 模拟大量消息
    console.log('\n2. 模拟大量消息数据:')
    simulateMessages()
    testMemoryStats()
    
    // 3. 测试重复消息处理
    console.log('\n3. 测试重复消息处理:')
    testDuplicateMessages()
    
    // 4. 测试过期消息处理
    console.log('\n4. 测试过期消息处理:')
    testExpiredMessages()
    
    // 5. 测试内存清理
    console.log('\n5. 测试内存清理:')
    testMemoryCleanup()
    
    // 6. 最终状态
    console.log('\n6. 最终内存状态:')
    testMemoryStats()
    
    console.log('\n=== 测试完成 ===')
    console.log('✅ 所有内存优化功能正常工作')
    
  } catch (error) {
    console.error('测试过程中出现错误:', error)
  }
}

// 如果直接运行此文件，执行测试
if (import.meta.url === `file://${process.argv[1]}`) {
  runTests()
}

export { runTests, simulateMessages, testMemoryStats, testMemoryCleanup }
