import { Config } from "../components/index.js"
import {
  saveMessage_id,
  findMessage_id,
  existSQL,
  findUser_id,
  saveUser_id,
  updateUser_id,
  findGroup_id,
  saveGroup_id,
  updateGroup_id,
} from "./db/index.js"

// 存储5分钟内的消息，按群组/用户ID分组，每个组内存储消息数组
let recentMessages = {}

// 存储任务ID到用户ID的映射关系
let taskUserMapping = {}

// 内存使用统计
let memoryStats = {
  lastCleanup: Date.now(),
  messagesCount: 0,
  groupsCount: 0,
  taskMappingsCount: 0,
}

// 定时清理过期的消息数据（每分钟清理一次）
const cleanupInterval = setInterval(() => {
  const now = Math.floor(Date.now() / 1000)
  const fiveMinutesAgo = now - 5 * 60
  let cleanedMessages = 0
  let cleanedGroups = 0
  let cleanedTasks = 0

  // 清理过期消息
  for (const id in recentMessages) {
    if (recentMessages[id]) {
      const originalLength = recentMessages[id].length
      // 过滤掉5分钟前的消息
      recentMessages[id] = recentMessages[id].filter(msg => msg.time >= fiveMinutesAgo)
      cleanedMessages += originalLength - recentMessages[id].length

      // 如果数组为空，删除这个key
      if (recentMessages[id].length === 0) {
        delete recentMessages[id]
        cleanedGroups++
      }
    }
  }

  // 清理过期的任务映射
  for (const taskId in taskUserMapping) {
    if (taskUserMapping[taskId] && now - taskUserMapping[taskId].timestamp >= 5 * 60) {
      delete taskUserMapping[taskId]
      cleanedTasks++
    }
  }

  // 更新统计信息
  memoryStats.lastCleanup = Date.now()
  memoryStats.messagesCount = Object.values(recentMessages).reduce(
    (total, msgs) => total + msgs.length,
    0,
  )
  memoryStats.groupsCount = Object.keys(recentMessages).length
  memoryStats.taskMappingsCount = Object.keys(taskUserMapping).length

  // 如果清理了大量数据，记录日志
  if (cleanedMessages > 100 || cleanedGroups > 10 || cleanedTasks > 50) {
    logger.info(
      `[ws-plugin] 内存清理完成: 清理消息${cleanedMessages}条, 群组${cleanedGroups}个, 任务映射${cleanedTasks}个`,
    )
    logger.info(
      `[ws-plugin] 当前内存使用: 消息${memoryStats.messagesCount}条, 群组${memoryStats.groupsCount}个, 任务映射${memoryStats.taskMappingsCount}个`,
    )
  }

  // 强制垃圾回收（如果可用）
  if (global.gc && (cleanedMessages > 500 || memoryStats.messagesCount > 10000)) {
    global.gc()
    logger.info("[ws-plugin] 执行强制垃圾回收")
  }
}, 60 * 1000) // 每分钟执行一次清理

// 进程退出时清理定时器
process.on("exit", () => {
  if (cleanupInterval) {
    clearInterval(cleanupInterval)
  }
})

async function getMsg(where, other) {
  if (Object.hasOwnProperty.call(where, "message_id") && where.message_id == undefined) {
    return null
  }
  if (existSQL) {
    return await findMessage_id(where, other)
  } else {
    let key = where.onebot_id || where.message_id
    let msg = await redis.get(`Yz:ws-plugin:msg:${key}`)
    if (!msg) {
      return null
    }
    return JSON.parse(msg)
  }
}

async function setMsg(value) {
  if (Array.isArray(value.message_id) || !value.seq || !value.rand) {
    return
  }
  if (existSQL) {
    await saveMessage_id(value)
  } else {
    const EX = Config.msgStoreTime
    if (EX > 0) {
      await redis.set(`Yz:ws-plugin:msg:${value.onebot_id}`, JSON.stringify(value), { EX })
      await redis.set(`Yz:ws-plugin:msg:${value.message_id}`, JSON.stringify(value), { EX })
    }
  }
}

/**
 * 小于五分钟才会返回
 * @param {*} id
 * @returns
 */
function getLatestMsg(id) {
  const messages = recentMessages[id]
  if (!messages || messages.length === 0) {
    return null
  }

  const now = Math.floor(Date.now() / 1000)
  const fiveMinutesAgo = now - 5 * 60

  // 过滤5分钟内的消息，返回最新的一条
  const validMessages = messages.filter(msg => msg.time >= fiveMinutesAgo)

  if (validMessages.length === 0) {
    return null
  }

  // 返回最新的消息（保持原有接口不变）
  return validMessages[validMessages.length - 1]
}

/**
 * 设置对应的id的最新数据（现在存储5分钟内的所有消息）
 * @param {string|number} id 群组ID或用户ID
 * @param {Object} data 消息数据
 * @param {number} data.time 消息时间戳
 * @param {string|number} data.user_id 用户ID
 * @param {Function|null} data.reply 回复函数
 * @param {string} data.message_id 消息ID
 */
function setLatestMsg(id, data) {
  // 输入验证
  if (!id || !data) {
    return
  }

  // 转换ID为字符串，避免类型不一致导致的内存泄漏
  id = String(id)

  if (!recentMessages[id]) {
    recentMessages[id] = []
  }

  // 确保data有时间戳
  if (!data.time) {
    data.time = Math.floor(Date.now() / 1000)
  }

  // 检查是否是重复消息（避免重复存储）
  const existingMsg = recentMessages[id].find(
    msg => msg.message_id === data.message_id && msg.user_id === data.user_id,
  )

  if (existingMsg) {
    // 更新现有消息而不是添加新的
    Object.assign(existingMsg, data)
    return
  }

  // 清理过期消息（在添加新消息时）
  const now = Math.floor(Date.now() / 1000)
  const fiveMinutesAgo = now - 5 * 60
  recentMessages[id] = recentMessages[id].filter(msg => msg.time >= fiveMinutesAgo)

  // 添加消息到数组
  recentMessages[id].push(data)

  // 限制数组长度，避免内存过度使用（保留最近30条消息，减少内存占用）
  if (recentMessages[id].length > 30) {
    recentMessages[id] = recentMessages[id].slice(-30)
  }

  // 全局内存保护：如果总群组数超过限制，清理最旧的群组
  const totalGroups = Object.keys(recentMessages).length
  if (totalGroups > 1000) {
    // 找到最旧的群组并删除
    let oldestGroup = null
    let oldestTime = now

    for (const groupId in recentMessages) {
      const messages = recentMessages[groupId]
      if (messages && messages.length > 0) {
        const groupOldestTime = Math.min(...messages.map(msg => msg.time))
        if (groupOldestTime < oldestTime) {
          oldestTime = groupOldestTime
          oldestGroup = groupId
        }
      }
    }

    if (oldestGroup) {
      delete recentMessages[oldestGroup]
      logger.warn(`[ws-plugin] 内存保护: 删除最旧群组 ${oldestGroup} 的消息缓存`)
    }
  }
}

/**
 * 根据消息ID获取消息上下文（用于回复时正确艾特）
 * @param {string} messageId 消息ID
 * @returns {Object|null} 消息上下文或null
 */
function getMessageContext(messageId) {
  for (const groupId in recentMessages) {
    const messages = recentMessages[groupId]
    if (messages) {
      const message = messages.find(msg => msg.message_id === messageId)
      if (message) {
        const now = Math.floor(Date.now() / 1000)
        // 只返回5分钟内的消息
        if (now - message.time < 5 * 60) {
          return message
        }
      }
    }
  }
  return null
}

/**
 * 根据用户ID和群组ID获取用户的最新消息上下文
 * @param {string} groupId 群组ID
 * @param {string} userId 用户ID
 * @returns {Object|null} 用户的最新消息上下文或null
 */
function getUserLatestMsg(groupId, userId) {
  if (!groupId || !userId) {
    return null
  }

  const messages = recentMessages[groupId]
  if (!messages || messages.length === 0) {
    return null
  }

  const now = Math.floor(Date.now() / 1000)
  const fiveMinutesAgo = now - 5 * 60

  // 查找5分钟内该用户的消息，按时间倒序
  const userMessages = messages
    .filter(msg => msg.time >= fiveMinutesAgo && msg.user_id === userId)
    .sort((a, b) => b.time - a.time)

  if (userMessages.length === 0) {
    return null
  }

  // 返回该用户最新的消息
  return userMessages[0]
}

/**
 * 设置任务ID到用户信息的映射
 * @param {string} taskId 任务ID
 * @param {string} userId 用户ID
 * @param {string} groupId 群组ID
 */
function setTaskUserMapping(taskId, userId, groupId) {
  if (taskId && userId) {
    taskUserMapping[taskId] = {
      user_id: userId,
      group_id: groupId,
      timestamp: Math.floor(Date.now() / 1000),
    }
  }
}

/**
 * 根据任务ID获取用户信息
 * @param {string} taskId 任务ID
 * @returns {Object|null} 用户信息或null
 */
function getUserByTaskId(taskId) {
  if (!taskId || !taskUserMapping[taskId]) {
    return null
  }

  const mapping = taskUserMapping[taskId]
  const now = Math.floor(Date.now() / 1000)

  // 只返回5分钟内的映射
  if (now - mapping.timestamp < 5 * 60) {
    return mapping
  }

  // 清理过期的映射
  delete taskUserMapping[taskId]
  return null
}

async function getUser_id(where) {
  if (where.user_id) {
    if (!isNaN(Number(where.user_id))) {
      return Number(where.user_id)
    }
    where.user_id = String(where.user_id)
  }
  let data = await findUser_id(where)
  if (!data) {
    if (where.user_id) {
      data = await saveUser_id(where.user_id)
    } else {
      return where.custom || where.id
    }
  }
  if (where.user_id) {
    return Number(data.custom) || data.id
  } else {
    return data.user_id
  }
}

async function setUser_id(where, custom) {
  const user_id = Number(custom)
  if (isNaN(user_id)) {
    return "输入有误,ID应为纯数字"
  }
  const result = await updateUser_id(where, user_id)
  if (result[0]) {
    return `修改成功~\n${where.user_id} => ${custom}`
  }
  return "修改失败,未包含此ID"
}

async function getGroup_id(where) {
  if (where.group_id) {
    if (!isNaN(Number(where.group_id))) {
      return Number(where.group_id)
    }
    where.group_id = String(where.group_id)
  }
  let data = await findGroup_id(where)
  if (!data) {
    if (where.group_id) {
      data = await saveGroup_id(where.group_id)
    } else {
      return where.custom || where.id
    }
  }
  if (where.group_id) {
    return Number(data.custom) || data.id
  } else {
    return data.group_id
  }
}

async function setGroup_id(where, custom) {
  const group_id = Number(custom)
  if (isNaN(group_id)) {
    return "输入有误,ID应为纯数字"
  }
  const result = await updateGroup_id(where, group_id)
  if (result[0]) {
    return `修改成功~\n${where.group_id} => ${custom}`
  }
  return "修改失败,未包含此ID"
}

/**
 * 获取内存使用统计信息
 * @returns {Object} 内存统计信息
 */
function getMemoryStats() {
  const now = Date.now()
  const currentStats = {
    ...memoryStats,
    messagesCount: Object.values(recentMessages).reduce((total, msgs) => total + msgs.length, 0),
    groupsCount: Object.keys(recentMessages).length,
    taskMappingsCount: Object.keys(taskUserMapping).length,
    lastUpdate: now,
    timeSinceLastCleanup: now - memoryStats.lastCleanup,
  }

  return currentStats
}

/**
 * 手动清理内存
 * @returns {Object} 清理结果统计
 */
function manualCleanup() {
  const now = Math.floor(Date.now() / 1000)
  const fiveMinutesAgo = now - 5 * 60
  let cleanedMessages = 0
  let cleanedGroups = 0
  let cleanedTasks = 0

  // 清理过期消息
  for (const id in recentMessages) {
    if (recentMessages[id]) {
      const originalLength = recentMessages[id].length
      recentMessages[id] = recentMessages[id].filter(msg => msg.time >= fiveMinutesAgo)
      cleanedMessages += originalLength - recentMessages[id].length

      if (recentMessages[id].length === 0) {
        delete recentMessages[id]
        cleanedGroups++
      }
    }
  }

  // 清理过期的任务映射
  for (const taskId in taskUserMapping) {
    if (taskUserMapping[taskId] && now - taskUserMapping[taskId].timestamp >= 5 * 60) {
      delete taskUserMapping[taskId]
      cleanedTasks++
    }
  }

  // 强制垃圾回收
  if (global.gc) {
    global.gc()
  }

  const result = {
    cleanedMessages,
    cleanedGroups,
    cleanedTasks,
    timestamp: Date.now(),
  }

  logger.info(`[ws-plugin] 手动清理完成: ${JSON.stringify(result)}`)
  return result
}

export {
  getMsg,
  setMsg,
  getLatestMsg,
  setLatestMsg,
  getMessageContext,
  getUserLatestMsg,
  setTaskUserMapping,
  getUserByTaskId,
  getUser_id,
  setUser_id,
  getGroup_id,
  setGroup_id,
  getMemoryStats,
  manualCleanup,
}
