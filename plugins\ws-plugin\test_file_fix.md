# ws-plugin 文件发送修复说明

## 问题描述
使用ws-plugin连接gsuid_core时，bot接收到的文件无法正确发送到gsuid_core。

## 根本原因
1. **接收文件处理问题**：
   - 原代码只处理私聊文件，群文件被跳过
   - 文件数据格式不符合gsuid_core协议要求

2. **发送文件处理问题**：
   - 只处理群文件上传，私聊文件发送逻辑缺失
   - 错误处理不完善

## 修复内容

### 1. 修复文件接收逻辑 (makeGSUidReportMsg函数)
- 支持群聊和私聊文件处理
- 兼容TRSS版本和原版Yunzai
- 添加错误处理和日志记录
- 确保文件格式符合gsuid_core协议：`{文件名}|{文件base64}`

### 2. 修复文件发送逻辑 (makeGSUidSendMsg函数)
- 群聊：使用fs.upload上传文件
- 私聊：使用segment.file发送文件
- 添加数据格式验证和错误处理

### 3. 完善OneBot协议支持
- 在makeSendMsg函数中添加file类型处理
- 在msgToOneBotMsg函数中添加file类型上报

## gsuid_core协议要求
根据文档，文件消息格式为：
```
type: 'file'
data: '{文件名}|{文件base64}'
```

## 详细修复内容

### 1. makeGSUidReportMsg函数修复 (第81-138行)
**原问题**：
- 只处理私聊文件：`if (e.isGroup || Version.isTrss) break`
- 缺少错误处理

**修复后**：
```javascript
case 'file': {
  try {
    let fileUrl, buffer, base64, name

    if (e.isGroup) {
      // 群文件处理
      if (e.file && e.file.fid) {
        fileUrl = await e.group?.getFileUrl?.(e.file.fid)
        name = e.file.name || i.name || 'unknown_file'
      } else {
        // 处理其他类型的文件消息
        name = i.name || 'unknown_file'
        if (i.url) {
          fileUrl = i.url
        } else {
          break
        }
      }
    } else {
      // 私聊文件处理，兼容TRSS和原版
      // ... 详细处理逻辑
    }

    if (fileUrl) {
      let res = await fetch(fileUrl)
      let arrayBuffer = await res.arrayBuffer()
      buffer = Buffer.from(arrayBuffer)
      base64 = buffer.toString('base64')

      message.push({
        type: 'file',
        data: `${name}|${base64}`
      })
    }
  } catch (error) {
    logger.error('[ws-plugin] 处理文件消息失败:', error)
  }
  break
}
```

### 2. makeGSUidSendMsg函数修复 (第228-250行)
**原问题**：
- 只处理群文件上传
- 私聊文件发送逻辑缺失

**修复后**：
```javascript
case 'file': {
  try {
    let file = msg.data.split('|')
    if (file.length >= 2) {
      let fileName = file[0]
      let base64Data = file[1]
      let buffer = Buffer.from(base64Data, 'base64')

      if (data.target_type === 'group') {
        // 群聊文件上传
        await bot.pickGroup(data.target_id)?.fs?.upload?.(buffer, '/', fileName)
      } else if (data.target_type === 'direct') {
        // 私聊文件发送
        sendMsg.push(segment.file(buffer, fileName))
      }
    } else {
      logger.warn('[ws-plugin] 文件数据格式错误:', msg.data)
    }
  } catch (error) {
    logger.error('[ws-plugin] 发送文件失败:', error)
  }
  break
}
```

### 3. makeSendMsg函数补充 (第459-467行)
**添加内容**：
```javascript
case 'file':
  sendMsg.push(segment.file(i.data.file, i.data.name))
  break
```

### 4. msgToOneBotMsg函数补充 (第653-672行)
**添加内容**：
```javascript
case 'file':
  reportMsg.push({
    type: 'file',
    data: {
      file: i.file,
      name: i.name,
      url: i.url,
    },
  })
  break
```

## 测试建议
1. 测试群聊文件发送到gsuid_core
2. 测试私聊文件发送到gsuid_core
3. 测试从gsuid_core接收文件并转发
4. 验证错误处理是否正常工作
5. 运行测试脚本：`node plugins/ws-plugin/test_file_handling.js`

## 兼容性
- 支持Miao-Yunzai和TRSS-Yunzai
- 向后兼容原有功能
- 不影响其他连接类型的文件处理
- 完全符合gsuid_core协议要求

## 文件格式说明
根据gsuid_core文档，文件消息格式为：
- **接收时**：从Bot平台获取文件URL，下载并转换为base64
- **发送时**：解析`{文件名}|{文件base64}`格式
- **群聊**：使用fs.upload上传到群文件
- **私聊**：使用segment.file发送文件
