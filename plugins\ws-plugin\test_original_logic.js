// 测试完全按照原版逻辑修复后的功能

// 模拟全局变量
global.Bot = {}
global.logger = {
  info: console.log,
  warn: console.warn,
  error: console.error,
  mark: console.log
}

global.segment = {
  file: (buffer, name) => ({ type: 'file', buffer, name })
}

global.Version = {
  isTrss: false
}

// 测试原版重复消息检查逻辑
function testOriginalDuplicateLogic() {
  console.log('=== 测试原版风格的重复消息检查逻辑 ===')
  
  // 模拟消息去重缓存
  const recentMsgIds = new Map()
  
  // 测试函数 - 模拟原版简化逻辑
  function checkDuplicate(data) {
    if (data.msg_id) {
      const now = Date.now()
      
      // 检查是否在很短时间内处理过完全相同的消息ID
      if (recentMsgIds.has(data.msg_id)) {
        const lastProcessTime = recentMsgIds.get(data.msg_id)
        if (now - lastProcessTime < 1000) {
          // 1秒内完全相同msg_id的消息直接忽略（真正的重复）
          return true
        }
      }
      
      // 记录消息处理时间
      recentMsgIds.set(data.msg_id, now)
      return false
    }
    return false
  }
  
  // 测试1: 连续发送两个不同图片（不同msg_id）
  console.log('\n1. 测试连续发送两个不同图片:')
  const img1 = {
    msg_id: 'img001',
    target_type: 'group',
    target_id: '456789',
    content: [{ type: 'image', data: 'image1.jpg' }]
  }
  
  const img2 = {
    msg_id: 'img002', // 不同的msg_id
    target_type: 'group',
    target_id: '456789',
    content: [{ type: 'image', data: 'image2.jpg' }]
  }
  
  const isDupImg1 = checkDuplicate(img1)
  const isDupImg2 = checkDuplicate(img2)
  console.log(`  图片1 (msg_id: img001): ${isDupImg1} (应该是false)`)
  console.log(`  图片2 (msg_id: img002): ${isDupImg2} (应该是false)`)
  
  // 测试2: 真正的重复消息（相同msg_id）
  console.log('\n2. 测试真正的重复消息:')
  const msg1 = {
    msg_id: 'test123',
    target_type: 'group',
    target_id: '456789',
    content: [{ type: 'text', data: 'hello' }]
  }
  
  const isDup1 = checkDuplicate(msg1)
  console.log(`  消息1: ${isDup1} (应该是false)`)
  
  // 立即重复相同msg_id
  const isDup2 = checkDuplicate(msg1)
  console.log(`  立即重复的消息1: ${isDup2} (应该是true)`)
  
  // 测试3: 1秒后相同msg_id（应该不被拦截）
  console.log('\n3. 测试1秒后相同msg_id:')
  setTimeout(() => {
    const isDup3 = checkDuplicate(msg1)
    console.log(`  1秒后相同msg_id: ${isDup3} (应该是false)`)
  }, 1100)
}

// 测试原版文件处理逻辑
async function testOriginalFileLogic() {
  console.log('\n=== 测试原版文件处理逻辑 ===')
  
  // 模拟fetch
  global.fetch = async (url) => {
    console.log(`  下载文件: ${url}`)
    const testData = JSON.stringify({ test: 'data' })
    return {
      arrayBuffer: async () => new TextEncoder().encode(testData).buffer
    }
  }
  
  // 测试1: 群聊文件处理（应该被跳过）
  console.log('\n1. 测试群聊文件处理（原版逻辑会跳过）:')
  const isGroup = true
  const isTrss = false
  if (isGroup || isTrss) {
    console.log('  ✓ 群聊文件处理被正确跳过（符合原版逻辑）')
  } else {
    console.log('  ✗ 群聊文件处理没有被跳过')
  }
  
  // 测试2: 私聊文件处理（原版逻辑）
  console.log('\n2. 测试私聊文件处理（原版逻辑）:')
  const mockFileData = {
    isGroup: false,
    file: { fid: 'test123' },
    friend: {
      getFileUrl: async (fid) => {
        console.log(`  获取文件URL: ${fid}`)
        return 'http://example.com/test.json'
      }
    }
  }
  
  const mockItem = { name: 'test.json' }
  
  try {
    // 模拟原版逻辑
    if (!mockFileData.isGroup && !Version.isTrss) {
      const fileUrl = await mockFileData.friend.getFileUrl(mockFileData.file.fid)
      const res = await fetch(fileUrl)
      const arrayBuffer = await res.arrayBuffer()
      const buffer = Buffer.from(arrayBuffer)
      const base64 = buffer.toString('base64')
      const name = mockItem.name
      
      const fileData = `${name}|${base64}`
      console.log(`  ✓ 私聊文件处理成功: ${name}, base64长度: ${base64.length}`)
      console.log(`  文件数据格式: ${fileData.substring(0, 50)}...`)
    } else {
      console.log('  文件处理被跳过')
    }
  } catch (error) {
    console.log(`  ✗ 文件处理失败: ${error.message}`)
  }
  
  // 测试3: 原版文件发送逻辑
  console.log('\n3. 测试原版文件发送逻辑:')
  
  // 模拟Bot
  global.Bot = {
    pickGroup: (id) => ({
      fs: {
        upload: async (buffer, path, filename) => {
          console.log(`  ✓ 群聊文件上传成功: 群${id}, 路径${path}, 文件${filename}, 大小${buffer.length}bytes`)
          return true
        }
      }
    })
  }
  
  const mockSendData = 'test.json|eyJ0ZXN0IjoiZGF0YSJ9' // {"test":"data"} in base64
  const fileParts = mockSendData.split('|')
  
  if (fileParts.length >= 2) {
    const fileName = fileParts[0]
    const base64Data = fileParts[1]
    const buffer = Buffer.from(base64Data, 'base64')
    
    console.log(`  文件解析: ${fileName}, 大小: ${buffer.length} bytes`)
    
    // 原版逻辑：只上传到群聊
    try {
      await global.Bot.pickGroup('123456').fs.upload(buffer, '/', fileName)
    } catch (error) {
      console.log(`    ✗ 群聊上传失败: ${error.message}`)
    }
  } else {
    console.log('  ✗ 文件数据格式错误')
  }
}

// 运行所有测试
async function runAllTests() {
  testOriginalDuplicateLogic()
  await testOriginalFileLogic()
  
  // 等待异步测试完成
  setTimeout(() => {
    console.log('\n=== 所有测试完成 ===')
    console.log('\n总结:')
    console.log('✓ 重复消息检查：只基于msg_id，不会误拦截不同内容')
    console.log('✓ 文件处理：完全按照原版逻辑，群聊跳过，私聊处理')
    console.log('✓ 文件发送：完全按照原版逻辑，只支持群聊上传')
  }, 2000)
}

runAllTests().catch(console.error)
