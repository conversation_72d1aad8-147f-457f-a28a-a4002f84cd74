// 测试TRSS和私聊文件支持，以及msg_id查找用户逻辑

// 模拟全局变量
global.Bot = {}
global.logger = {
  info: console.log,
  warn: console.warn,
  error: console.error,
  mark: console.log
}

global.segment = {
  file: (buffer, name) => ({ type: 'file', buffer, name })
}

// 测试TRSS和私聊文件处理
async function testTrssAndPrivateFileSupport() {
  console.log('=== 测试TRSS和私聊文件支持 ===')
  
  // 模拟fetch
  global.fetch = async (url) => {
    console.log(`  下载文件: ${url}`)
    const testData = JSON.stringify({ test: 'data' })
    return {
      arrayBuffer: async () => new TextEncoder().encode(testData).buffer
    }
  }
  
  // 测试1: TRSS环境下的私聊文件处理
  console.log('\n1. 测试TRSS环境下的私聊文件处理:')
  global.Version = { isTrss: true }
  
  const trssPrivateFileData = {
    isGroup: false,
    file: { fid: 'trss123', name: 'trss_file.json' },
    friend: {
      getFileUrl: async (fid) => {
        console.log(`  TRSS获取私聊文件URL: ${fid}`)
        return 'http://example.com/trss_file.json'
      }
    }
  }
  
  const mockItem = { name: 'trss_file.json' }
  
  try {
    // 新的逻辑：支持TRSS私聊文件
    if (!trssPrivateFileData.isGroup) {
      const fileUrl = await trssPrivateFileData.friend.getFileUrl(trssPrivateFileData.file.fid)
      if (fileUrl) {
        const res = await fetch(fileUrl)
        const arrayBuffer = await res.arrayBuffer()
        const buffer = Buffer.from(arrayBuffer)
        const base64 = buffer.toString('base64')
        const name = mockItem.name
        
        const fileData = `${name}|${base64}`
        console.log(`  ✓ TRSS私聊文件处理成功: ${name}, base64长度: ${base64.length}`)
        console.log(`  文件数据格式: ${fileData.substring(0, 50)}...`)
      }
    }
  } catch (error) {
    console.log(`  ✗ TRSS私聊文件处理失败: ${error.message}`)
  }
  
  // 测试2: TRSS环境下的群聊文件处理
  console.log('\n2. 测试TRSS环境下的群聊文件处理:')
  
  const trssGroupFileData = {
    isGroup: true,
    file: { fid: 'trss_group123', name: 'trss_group_file.json' },
    group: {
      getFileUrl: async (fid) => {
        console.log(`  TRSS获取群文件URL: ${fid}`)
        return 'http://example.com/trss_group_file.json'
      }
    }
  }
  
  try {
    // 新的逻辑：支持TRSS群聊文件
    if (trssGroupFileData.isGroup) {
      const fileUrl = await trssGroupFileData.group.getFileUrl(trssGroupFileData.file.fid)
      if (fileUrl) {
        const res = await fetch(fileUrl)
        const arrayBuffer = await res.arrayBuffer()
        const buffer = Buffer.from(arrayBuffer)
        const base64 = buffer.toString('base64')
        const name = trssGroupFileData.file.name
        
        const fileData = `${name}|${base64}`
        console.log(`  ✓ TRSS群聊文件处理成功: ${name}, base64长度: ${base64.length}`)
        console.log(`  文件数据格式: ${fileData.substring(0, 50)}...`)
      }
    }
  } catch (error) {
    console.log(`  ✗ TRSS群聊文件处理失败: ${error.message}`)
  }
  
  // 测试3: 原版环境下的私聊文件处理
  console.log('\n3. 测试原版环境下的私聊文件处理:')
  global.Version = { isTrss: false }
  
  const originalPrivateFileData = {
    isGroup: false,
    file: { fid: 'original123', name: 'original_file.json' },
    friend: {
      getFileUrl: async (fid) => {
        console.log(`  原版获取私聊文件URL: ${fid}`)
        return 'http://example.com/original_file.json'
      }
    }
  }
  
  try {
    if (!originalPrivateFileData.isGroup) {
      const fileUrl = await originalPrivateFileData.friend.getFileUrl(originalPrivateFileData.file.fid)
      if (fileUrl) {
        const res = await fetch(fileUrl)
        const arrayBuffer = await res.arrayBuffer()
        const buffer = Buffer.from(arrayBuffer)
        const base64 = buffer.toString('base64')
        const name = originalPrivateFileData.file.name
        
        const fileData = `${name}|${base64}`
        console.log(`  ✓ 原版私聊文件处理成功: ${name}, base64长度: ${base64.length}`)
        console.log(`  文件数据格式: ${fileData.substring(0, 50)}...`)
      }
    }
  } catch (error) {
    console.log(`  ✗ 原版私聊文件处理失败: ${error.message}`)
  }
}

// 测试文件发送支持
async function testFileSendSupport() {
  console.log('\n=== 测试文件发送支持 ===')
  
  // 模拟Bot
  global.Bot = {
    pickGroup: (id) => ({
      fs: {
        upload: async (buffer, path, filename) => {
          console.log(`  ✓ 群聊文件上传: 群${id}, 路径${path}, 文件${filename}, 大小${buffer.length}bytes`)
          return true
        }
      }
    })
  }
  
  const testFileData = 'test.json|eyJ0ZXN0IjoiZGF0YSJ9' // {"test":"data"} in base64
  const fileParts = testFileData.split('|')
  
  if (fileParts.length >= 2) {
    const fileName = fileParts[0]
    const base64Data = fileParts[1]
    const buffer = Buffer.from(base64Data, 'base64')
    
    console.log(`\n1. 测试群聊文件发送:`)
    console.log(`  文件解析: ${fileName}, 大小: ${buffer.length} bytes`)
    
    // 群聊文件上传
    try {
      await global.Bot.pickGroup('123456').fs.upload(buffer, '/', fileName)
    } catch (error) {
      console.log(`    ✗ 群聊上传失败: ${error.message}`)
    }
    
    console.log(`\n2. 测试私聊文件发送:`)
    // 私聊文件发送
    const fileSegment = segment.file(buffer, fileName)
    console.log(`  ✓ 私聊文件segment创建: ${JSON.stringify({
      type: fileSegment.type,
      name: fileSegment.name,
      bufferSize: fileSegment.buffer.length
    })}`)
  }
}

// 测试msg_id查找用户逻辑（模拟）
function testMsgIdUserLookup() {
  console.log('\n=== 测试msg_id查找用户逻辑 ===')
  
  // 模拟消息上下文存储
  const messageContexts = new Map()
  messageContexts.set('msg123', { user_id: 'user456', group_id: 'group789' })
  messageContexts.set('msg456', { user_id: 'user789', group_id: 'group789' })
  
  // 模拟用户最新消息存储
  const userLatestMsgs = new Map()
  userLatestMsgs.set('group789_user456', { reply: (msg) => console.log(`  ✓ 回复给用户user456: ${JSON.stringify(msg)}`) })
  userLatestMsgs.set('group789_user789', { reply: (msg) => console.log(`  ✓ 回复给用户user789: ${JSON.stringify(msg)}`) })
  
  // 模拟查找函数
  function getMessageContext(msgId) {
    return messageContexts.get(msgId)
  }
  
  function getUserLatestMsg(groupId, userId) {
    return userLatestMsgs.get(`${groupId}_${userId}`)
  }
  
  // 测试场景
  const testData = {
    msg_id: 'msg123',
    target_type: 'group',
    target_id: 'group789',
    bot_id: 'qqgroup'
  }
  
  console.log('\n1. 测试通过msg_id查找用户:')
  console.log(`  输入: msg_id=${testData.msg_id}, target_id=${testData.target_id}`)
  
  // 模拟查找逻辑
  if (testData.msg_id) {
    const originalMsg = getMessageContext(testData.msg_id)
    if (originalMsg && originalMsg.user_id) {
      console.log(`  ✓ 找到原始消息用户: ${originalMsg.user_id}`)
      const msg = getUserLatestMsg(testData.target_id, originalMsg.user_id)
      if (msg && typeof msg.reply === 'function') {
        msg.reply(['测试回复消息'])
      }
    } else {
      console.log(`  ✗ 未找到msg_id对应的用户上下文`)
    }
  }
  
  console.log('\n2. 测试不存在的msg_id:')
  const testData2 = { ...testData, msg_id: 'nonexistent' }
  console.log(`  输入: msg_id=${testData2.msg_id}`)
  
  const originalMsg2 = getMessageContext(testData2.msg_id)
  if (!originalMsg2) {
    console.log(`  ✓ 正确处理不存在的msg_id，将使用备用方案`)
  }
}

// 运行所有测试
async function runAllTests() {
  await testTrssAndPrivateFileSupport()
  await testFileSendSupport()
  testMsgIdUserLookup()
  
  console.log('\n=== 所有测试完成 ===')
  console.log('\n总结:')
  console.log('✓ TRSS环境：支持私聊和群聊文件处理')
  console.log('✓ 原版环境：支持私聊文件处理')
  console.log('✓ 文件发送：支持群聊上传和私聊segment')
  console.log('✓ msg_id查找用户逻辑：完整保留，避免回复错误')
}

runAllTests().catch(console.error)
