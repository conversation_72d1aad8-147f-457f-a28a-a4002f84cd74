NjoxNiJ9LHsiY2FyZFBvb2xUeXBlIjoiNCIsInJlc291cmNlSWQiOjIxMDEwMDIzLCJxdWFsaXR5TGV2ZWwiOjMsInJlc291cmNlVHlwZSI6IuatpuWZqCIsIm5hbWUiOiLmupDog73plb/liIPCt+a1i+WjuSIsImNvdW50IjoxLCJ0aW1lIjoiMjAyNC0wNS0yOSAyMjoxNjoxNiJ9LHsiY2FyZFBvb2xUeXBlIjoiNCIsInJlc291cmNlSWQiOjIxMDQwMDQzLCJxdWFsaXR5TGV2ZWwiOjMsInJlc291cmNlVHlwZSI6IuatpuWZqCIsIm5hbWUiOiLov5zooYzogIXoh4Lpk6DCt+egtOmanCIsImNvdW50IjoxLCJ0aW1lIjoiMjAyNC0wNS0yOSAyMjoxNjoxNiJ9LHsiY2FyZFBvb2xUeXBlIjoiNCIsInJlc291cmNlSWQiOjIxMDMwMDIzLCJxdWFsaXR5TGV2ZWwiOjMsInJlc291cmNlVHlwZSI6IuatpuWZqCIsIm5hbWUiOiLmupDog73kvanmnqrCt+a1i+WPgSIsImNvdW50IjoxLCJ0aW1lIjoiMjAyNC0wNS0yOSAyMjoxNjoxNiJ9LHsiY2FyZFBvb2xUeXBlIjoiNCIsInJlc291cmNlSWQiOjIxMDUwMDIzLCJxdWFsaXR5TGV2ZWwiOjMsInJlc291cmNlVHlwZSI6IuatpuWZqCIsIm5hbWUiOiLmupDog73pn7PmhJ/ku6rCt+a1i+S6lCIsImNvdW50IjoxLCJ0aW1lIjoiMjAyNC0wNS0yOSAyMjoxNjoxNiJ9XX0=', file_type='base64', regex_group=(), regex_dict={})
2025-08-02 00:22:02 [info     ] [发送消息to] qqgroup - direct - 3889000318:4B8A766C5CA11917527B3FCD14830714
2025-08-02 00:22:02 [info     ] [发送消息to] qqgroup - direct - 3889000318:4B8A766C5CA11917527B3FCD14830714
2025-08-02 00:22:02 [info     ] [发送消息to] qqgroup - direct - 3889000318:4B8A766C5CA11917527B3FCD14830714
2025-08-02 00:22:05 [info     ] [发送消息to] qqgroup - direct - 3889000318:4B8A766C5CA11917527B3FCD14830714
2025-08-02 00:22:05 [error    ] [SV] json 执行时出现错误!
2025-08-02 00:22:05 [error    ] Expecting value: line 1 column 1 (char 0)
╭─────────────────────────────── Traceback (most recent call last) ────────────────────────────────╮
│ /root/gsuid_core/gsuid_core/sv.py:37 in wrapper                                                  │
│                                                                                                  │
│    34 │   @wraps(func)                                                                           │
│    35 │   async def wrapper(bot: Bot, event: Event):                                             │
│    36 │   │   try:                                                                               │
│ ❱  37 │   │   │   result = await func(bot, event)                                                │
│    38 │   │   │   return result                                                                  │
│    39 │   │   except Exception as e:                                                             │
│    40 │   │   │   logger.error(f'[SV] {event.command} 执行时出现错误!')                          │
│                                                                                                  │
│ ╭─────────────────────────────────────────── locals ───────────────────────────────────────────╮ │
│ │   bot = <gsuid_core.bot.Bot object at 0x7f7c816a0610>                                        │ │
│ │     e = JSONDecodeError('Expecting value: line 1 column 1 (char 0)')                         │ │
│ │ event = Event(                                                                               │ │
│ │         │   bot_id='qqgroup',                                                                │ │
│ │         │   bot_self_id='3889000318',                                                        │ │
│ │         │                                                                                    │ │
│ │         msg_id='ROBOT1.0_y1dvI-hJ.hzZd.txFtuXuP9BlGhbg5zfU2dQ3q.9dZf91tK5dQ8DAWXGOhKAi7ePUA… │ │
│ │         │   user_type='direct',                                                              │ │
│ │         │   group_id=None,                                                                   │ │
│ │         │   user_id='3889000318:4B8A766C5CA11917527B3FCD14830714',                           │ │
│ │         │   sender={                                                                         │ │
│ │         │   │   'user_id': '3889000318:4B8A766C5CA11917527B3FCD14830714',                    │ │
│ │         │   │   'avatar':                                                                    │ │
│ │         'https://q.qlogo.cn/qqapp/102072241/4B8A766C5CA11917527B3FCD14830714/0'              │ │
│ │         │   },                                                                               │ │
│ │         │   user_pm=0,                                                                       │ │
│ │         │   content=[                                                                        │ │
│ │         │   │   Message(                                                                     │ │
│ │         │   │   │   type='file',                                                             │ │
│ │         │   │   │                                                                            │ │
│ │         data='export_100381247.json|eyJpbmZvIjp7ImV4cG9ydF90aW1lIjoiMjAyNS0wOC0wMSAyMjoxODo… │ │
│ │         │   │   )                                                                            │ │
│ │         │   ],                                                                               │ │
│ │         │   task_id='bed9911d-62df-4d11-bba8-465df79f13d1',                                  │ │
│ │         │   task_event=None,                                                                 │ │
│ │         │   real_bot_id='qqgroup',                                                           │ │
│ │         │   raw_text='',                                                                     │ │
│ │         │   command='json',                                                                  │ │
│ │         │   text='',                                                                         │ │
│ │         │   image=None,                                                                      │ │
│ │         │   at=None,                                                                         │ │
│ │         │   image_list=[],                                                                   │ │
│ │         │   at_list=[],                                                                      │ │
│ │         │   is_tome=False,                                                                   │ │
│ │         │   reply=None,                                                                      │ │
│ │         │   file_name='export_100381247.json',                                               │ │
│ │         │                                                                                    │ │
│ │         file='eyJpbmZvIjp7ImV4cG9ydF90aW1lIjoiMjAyNS0wOC0wMSAyMjoxODo1MCIsImV4cG9ydF9hcHAiO… │ │
│ │         │   file_type='base64',                                                              │ │
│ │         │   regex_group=(),                                                                  │ │
│ │         │   regex_dict={}                                                                    │ │
│ │         )                                                                                    │ │
│ ╰──────────────────────────────────────────────────────────────────────────────────────────────╯ │
│                                                                                                  │
│ /root/gsuid_core/gsuid_core/plugins/WutheringWavesUID/WutheringWavesUID/wutheringwaves_gachalog/ │
│ __init__.py:102 in get_gacha_log_by_file                                                         │
│                                                                                                  │
│    99 │                                                                                          │
│   100 │   if ev.file and ev.file_type:                                                           │
│   101 │   │   await bot.send("正在尝试导入抽卡记录中，请耐心等待……")                             │
│ ❱ 102 │   │   return await bot.send(await import_gachalogs(ev, ev.file, ev.file_type, uid))      │
│   103 │   else:                                                                                  │
│   104 │   │   return await bot.send("导入抽卡记录异常...")                                       │
│   105                                                                                            │
│                                                                                                  │
│ ╭─────────────────────────────────────────── locals ───────────────────────────────────────────╮ │
│ │ bot = <gsuid_core.bot.Bot object at 0x7f7c816a0610>                                          │ │
│ │  ev = Event(                                                                                 │ │
│ │       │   bot_id='qqgroup',                                                                  │ │
│ │       │   bot_self_id='3889000318',                                                          │ │
│ │       │                                                                                      │ │
│ │       msg_id='ROBOT1.0_y1dvI-hJ.hzZd.txFtuXuP9BlGhbg5zfU2dQ3q.9dZf91tK5dQ8DAWXGOhKAi7ePUAIc… │ │
│ │       │   user_type='direct',                                                                │ │
│ │       │   group_id=None,                                                                     │ │
│ │       │   user_id='3889000318:4B8A766C5CA11917527B3FCD14830714',                             │ │
│ │       │   sender={                                                                           │ │
│ │       │   │   'user_id': '3889000318:4B8A766C5CA11917527B3FCD14830714',                      │ │
│ │       │   │   'avatar':                                                                      │ │
│ │       'https://q.qlogo.cn/qqapp/102072241/4B8A766C5CA11917527B3FCD14830714/0'                │ │
│ │       │   },                                                                                 │ │
│ │       │   user_pm=0,                                                                         │ │
│ │       │   content=[                                                                          │ │
│ │       │   │   Message(                                                                       │ │
│ │       │   │   │   type='file',                                                               │ │
│ │       │   │   │                                                                              │ │
│ │       data='export_100381247.json|eyJpbmZvIjp7ImV4cG9ydF90aW1lIjoiMjAyNS0wOC0wMSAyMjoxODo1M… │ │
│ │       │   │   )                                                                              │ │
│ │       │   ],                                                                                 │ │
│ │       │   task_id='bed9911d-62df-4d11-bba8-465df79f13d1',                                    │ │
│ │       │   task_event=None,                                                                   │ │
│ │       │   real_bot_id='qqgroup',                                                             │ │
│ │       │   raw_text='',                                                                       │ │
│ │       │   command='json',                                                                    │ │
│ │       │   text='',                                                                           │ │
│ │       │   image=None,                                                                        │ │
│ │       │   at=None,                                                                           │ │
│ │       │   image_list=[],                                                                     │ │
│ │       │   at_list=[],                                                                        │ │
│ │       │   is_tome=False,                                                                     │ │
│ │       │   reply=None,                                                                        │ │
│ │       │   file_name='export_100381247.json',                                                 │ │
│ │       │                                                                                      │ │
│ │       file='eyJpbmZvIjp7ImV4cG9ydF90aW1lIjoiMjAyNS0wOC0wMSAyMjoxODo1MCIsImV4cG9ydF9hcHAiOiJ… │ │
│ │       │   file_type='base64',                                                                │ │
│ │       │   regex_group=(),                                                                    │ │
│ │       │   regex_dict={}                                                                      │ │
│ │       )                                                                                      │ │
│ │ uid = '100381247'                                                                            │ │
│ ╰──────────────────────────────────────────────────────────────────────────────────────────────╯ │
│                                                                                                  │
│ /root/gsuid_core/gsuid_core/plugins/WutheringWavesUID/WutheringWavesUID/wutheringwaves_gachalog/ │
│ get_gachalogs.py:344 in import_gachalogs                                                         │
│                                                                                                  │
│   341 │   │   │   │   continue                                                                   │
│   342 │   │   import_data[gacha_name].append(GachaLog(**item.dict()))                            │
│   343 │                                                                                          │
│ ❱ 344 │   res = await save_gachalogs(ev, uid, "", import_data=import_data)                       │
│   345 │   return res                                                                             │
│   346                                                                                            │
│   347                                                                                            │
│                                                                                                  │
│ ╭─────────────────────────────────────────── locals ───────────────────────────────────────────╮ │
│ │   data_bytes = b'{"info":{"export_time":"2025-08-01                                          │ │
│ │                22:18:50","export_app":"WutheringWavesUID","e'+364506                         │ │
│ │           ev = Event(                                                                        │ │
│ │                │   bot_id='qqgroup',                                                         │ │
│ │                │   bot_self_id='3889000318',                                                 │ │
│ │                │                                                                             │ │
│ │                msg_id='ROBOT1.0_y1dvI-hJ.hzZd.txFtuXuP9BlGhbg5zfU2dQ3q.9dZf91tK5dQ8DAWXGOhK… │ │
│ │                │   user_type='direct',                                                       │ │
│ │                │   group_id=None,                                                            │ │
│ │                │   user_id='3889000318:4B8A766C5CA11917527B3FCD14830714',                    │ │
│ │                │   sender={                                                                  │ │
│ │                │   │   'user_id': '3889000318:4B8A766C5CA11917527B3FCD14830714',             │ │
│ │                │   │   'avatar':                                                             │ │
│ │                'https://q.qlogo.cn/qqapp/102072241/4B8A766C5CA11917527B3FCD14830714/0'       │ │
│ │                │   },                                                                        │ │
│ │                │   user_pm=0,                                                                │ │
│ │                │   content=[                                                                 │ │
│ │                │   │   Message(                                                              │ │
│ │                │   │   │   type='file',                                                      │ │
│ │                │   │   │                                                                     │ │
│ │                data='export_100381247.json|eyJpbmZvIjp7ImV4cG9ydF90aW1lIjoiMjAyNS0wOC0wMSAy… │ │
│ │                │   │   )                                                                     │ │
│ │                │   ],                                                                        │ │
│ │                │   task_id='bed9911d-62df-4d11-bba8-465df79f13d1',                           │ │
│ │                │   task_event=None,                                                          │ │
│ │                │   real_bot_id='qqgroup',                                                    │ │
│ │                │   raw_text='',                                                              │ │
│ │                │   command='json',                                                           │ │
│ │                │   text='',                                                                  │ │
│ │                │   image=None,                                                               │ │
│ │                │   at=None,                                                                  │ │
│ │                │   image_list=[],                                                            │ │
│ │                │   at_list=[],                                                               │ │
│ │                │   is_tome=False,                                                            │ │
│ │                │   reply=None,                                                               │ │
│ │                │   file_name='export_100381247.json',                                        │ │
│ │                │                                                                             │ │
│ │                file='eyJpbmZvIjp7ImV4cG9ydF90aW1lIjoiMjAyNS0wOC0wMSAyMjoxODo1MCIsImV4cG9ydF… │ │
│ │                │   file_type='base64',                                                       │ │
│ │                │   regex_group=(),                                                           │ │
│ │                │   regex_dict={}                                                             │ │
│ │                )                                                                             │ │
│ │   gacha_name = '武器调谐（常驻池）'                                                          │ │
│ │ history_data = {                                                                             │ │
│ │                │   'info': {                                                                 │ │
│ │                │   │   'export_time': '2025-08-01 22:18:50',                                 │ │
│ │                │   │   'export_app': 'WutheringWavesUID',                                    │ │
│ │                │   │   'export_app_version': '2.5.0',                                        │ │
│ │                │   │   'export_timestamp': 1754057930,                                       │ │
│ │                │   │   'version': 'v2.0',                                                    │ │
│ │                │   │   'uid': '100381247'                                                    │ │
│ │                │   },                                                                        │ │
│ │                │   'list': [                                                                 │ │
│ │                │   │   {                                                                     │ │
│ │                │   │   │   'cardPoolType': '1',                                              │ │
│ │                │   │   │   'resourceId': 21030013,                                           │ │
│ │                │   │   │   'qualityLevel': 3,                                                │ │
│ │                │   │   │   'resourceType': '武器',                                           │ │
│ │                │   │   │   'name': '暗夜佩枪·暗星',                                          │ │
│ │                │   │   │   'count': 1,                                                       │ │
│ │                │   │   │   'time': '2025-07-26 21:12:28'                                     │ │
│ │                │   │   },                                                                    │ │
│ │                │   │   {                                                                     │ │
│ │                │   │   │   'cardPoolType': '1',                                              │ │
│ │                │   │   │   'resourceId': 21030023,                                           │ │
│ │                │   │   │   'qualityLevel': 3,                                                │ │
│ │                │   │   │   'resourceType': '武器',                                           │ │
│ │                │   │   │   'name': '源能佩枪·测叁',                                          │ │
│ │                │   │   │   'count': 1,                                                       │ │
│ │                │   │   │   'time': '2025-07-26 21:12:28'                                     │ │
│ │                │   │   },                                                                    │ │
│ │                │   │   {                                                                     │ │
│ │                │   │   │   'cardPoolType': '1',                                              │ │
│ │                │   │   │   'resourceId': 21010013,                                           │ │
│ │                │   │   │   'qualityLevel': 3,                                                │ │
│ │                │   │   │   'resourceType': '武器',                                           │ │
│ │                │   │   │   'name': '暗夜长刃·玄明',                                          │ │
│ │                │   │   │   'count': 1,                                                       │ │
│ │                │   │   │   'time': '2025-07-26 21:12:28'                                     │ │
│ │                │   │   },                                                                    │ │
│ │                │   │   {                                                                     │ │
│ │                │   │   │   'cardPoolType': '1',                                              │ │
│ │                │   │   │   'resourceId': 21020013,                                           │ │
│ │                │   │   │   'qualityLevel': 3,                                                │ │
│ │                │   │   │   'resourceType': '武器',                                           │ │
│ │                │   │   │   'name': '暗夜迅刀·黑闪',                                          │ │
│ │                │   │   │   'count': 1,                                                       │ │
│ │                │   │   │   'time': '2025-07-26 21:12:28'                                     │ │
│ │                │   │   },                                                                    │ │
│ │                │   │   {                                                                     │ │
│ │                │   │   │   'cardPoolType': '1',                                              │ │
│ │                │   │   │   'resourceId': 21040013,                                           │ │
│ │                │   │   │   'qualityLevel': 3,                                                │ │
│ │                │   │   │   'resourceType': '武器',                                           │ │
│ │                │   │   │   'name': '暗夜臂铠·夜芒',                                          │ │
│ │                │   │   │   'count': 1,                                                       │ │
│ │                │   │   │   'time': '2025-07-26 21:12:28'                                     │ │
│ │                │   │   },                                                                    │ │
│ │                │   │   {                                                                     │ │
│ │                │   │   │   'cardPoolType': '1',                                              │ │
│ │                │   │   │   'resourceId': 21010013,                                           │ │
│ │                │   │   │   'qualityLevel': 3,                                                │ │
│ │                │   │   │   'resourceType': '武器',                                           │ │
│ │                │   │   │   'name': '暗夜长刃·玄明',                                          │ │
│ │                │   │   │   'count': 1,                                                       │ │
│ │                │   │   │   'time': '2025-07-26 21:12:28'                                     │ │
│ │                │   │   },                                                                    │ │
│ │                │   │   {                                                                     │ │
│ │                │   │   │   'cardPoolType': '1',                                              │ │
│ │                │   │   │   'resourceId': 21020023,                                           │ │
│ │                │   │   │   'qualityLevel': 3,                                                │ │
│ │                │   │   │   'resourceType': '武器',                                           │ │
│ │                │   │   │   'name': '源能迅刀·测贰',                                          │ │
│ │                │   │   │   'count': 1,                                                       │ │
│ │                │   │   │   'time': '2025-07-26 21:12:28'                                     │ │
│ │                │   │   },                                                                    │ │
│ │                │   │   {                                                                     │ │
│ │                │   │   │   'cardPoolType': '1',                                              │ │
│ │                │   │   │   'resourceId': 21010013,                                           │ │
│ │                │   │   │   'qualityLevel': 3,                                                │ │
│ │                │   │   │   'resourceType': '武器',                                           │ │
│ │                │   │   │   'name': '暗夜长刃·玄明',                                          │ │
│ │                │   │   │   'count': 1,                                                       │ │
│ │                │   │   │   'time': '2025-07-26 21:12:28'                                     │ │
│ │                │   │   },                                                                    │ │
│ │                │   │   {                                                                     │ │
│ │                │   │   │   'cardPoolType': '1',                                              │ │
│ │                │   │   │   'resourceId': 1608,                                               │ │
│ │                │   │   │   'qualityLevel': 5,                                                │ │
│ │                │   │   │   'resourceType': '角色',                                           │ │
│ │                │   │   │   'name': '弗洛洛',                                                 │ │
│ │                │   │   │   'count': 1,                                                       │ │
│ │                │   │   │   'time': '2025-07-26 21:12:28'                                     │ │
│ │                │   │   },                                                                    │ │
│ │                │   │   {                                                                     │ │
│ │                │   │   │   'cardPoolType': '1',                                              │ │
│ │                │   │   │   'resourceId': 21050043,                                           │ │
│ │                │   │   │   'qualityLevel': 3,                                                │ │
│ │                │   │   │   'resourceType': '武器',                                           │ │
│ │                │   │   │   'name': '远行者矩阵·探幽',                                        │ │
│ │                │   │   │   'count': 1,                                                       │ │
│ │                │   │   │   'time': '2025-07-26 21:12:28'                                     │ │
│ │                │   │   },                                                                    │ │
│ │                │   │   ... +2385                                                             │ │
│ │                │   ]                                                                         │ │
│ │                }                                                                             │ │
│ │  history_url = 'eyJpbmZvIjp7ImV4cG9ydF90aW1lIjoiMjAyNS0wOC0wMSAyMjoxODo1MCIsImV4cG9ydF9hcHA… │ │
│ │  import_data = {                                                                             │ │
│ │                │   '角色精准调谐': [                                                         │ │
│ │                │   │   GachaLog(                                                             │ │
│ │                │   │   │   cardPoolType='1',                                                 │ │
│ │                │   │   │   resourceId=21030013,                                              │ │
│ │                │   │   │   qualityLevel=3,                                                   │ │
│ │                │   │   │   resourceType='武器',                                              │ │
│ │                │   │   │   name='暗夜佩枪·暗星',                                             │ │
│ │                │   │   │   count=1,                                                          │ │
│ │                │   │   │   time='2025-07-26 21:12:28'                                        │ │
│ │                │   │   ),                                                                    │ │
│ │                │   │   GachaLog(                                                             │ │
│ │                │   │   │   cardPoolType='1',                                                 │ │
│ │                │   │   │   resourceId=21030023,                                              │ │
│ │                │   │   │   qualityLevel=3,                                                   │ │
│ │                │   │   │   resourceType='武器',                                              │ │
│ │                │   │   │   name='源能佩枪·测叁',                                             │ │
│ │                │   │   │   count=1,                                                          │ │
│ │                │   │   │   time='2025-07-26 21:12:28'                                        │ │
│ │                │   │   ),                                                                    │ │
│ │                │   │   GachaLog(                                                             │ │
│ │                │   │   │   cardPoolType='1',                                                 │ │
│ │                │   │   │   resourceId=21010013,                                              │ │
│ │                │   │   │   qualityLevel=3,                                                   │ │
│ │                │   │   │   resourceType='武器',                                              │ │
│ │                │   │   │   name='暗夜长刃·玄明',                                             │ │
│ │                │   │   │   count=1,                                                          │ │
│ │                │   │   │   time='2025-07-26 21:12:28'                                        │ │
│ │                │   │   ),                                                                    │ │
│ │                │   │   GachaLog(                                                             │ │
│ │                │   │   │   cardPoolType='1',                                                 │ │
│ │                │   │   │   resourceId=21020013,                                              │ │
│ │                │   │   │   qualityLevel=3,                                                   │ │
│ │                │   │   │   resourceType='武器',                                              │ │
│ │                │   │   │   name='暗夜迅刀·黑闪',                                             │ │
│ │                │   │   │   count=1,                                                          │ │
│ │                │   │   │   time='2025-07-26 21:12:28'                                        │ │
│ │                │   │   ),                                                                    │ │
│ │                │   │   GachaLog(                                                             │ │
│ │                │   │   │   cardPoolType='1',                                                 │ │
│ │                │   │   │   resourceId=21040013,                                              │ │
│ │                │   │   │   qualityLevel=3,                                                   │ │
│ │                │   │   │   resourceType='武器',                                              │ │
│ │                │   │   │   name='暗夜臂铠·夜芒',                                             │ │
│ │                │   │   │   count=1,                                                          │ │
│ │                │   │   │   time='2025-07-26 21:12:28'                                        │ │
│ │                │   │   ),                                                                    │ │
│ │                │   │   GachaLog(                                                             │ │
│ │                │   │   │   cardPoolType='1',                                                 │ │
│ │                │   │   │   resourceId=21010013,                                              │ │
│ │                │   │   │   qualityLevel=3,                                                   │ │
│ │                │   │   │   resourceType='武器',                                              │ │
│ │                │   │   │   name='暗夜长刃·玄明',                                             │ │
│ │                │   │   │   count=1,                                                          │ │
│ │                │   │   │   time='2025-07-26 21:12:28'                                        │ │
│ │                │   │   ),                                                                    │ │
│ │                │   │   GachaLog(                                                             │ │
│ │                │   │   │   cardPoolType='1',                                                 │ │
│ │                │   │   │   resourceId=21020023,                                              │ │
│ │                │   │   │   qualityLevel=3,                                                   │ │
│ │                │   │   │   resourceType='武器',                                              │ │
│ │                │   │   │   name='源能迅刀·测贰',                                             │ │
│ │                │   │   │   count=1,                                                          │ │
│ │                │   │   │   time='2025-07-26 21:12:28'                                        │ │
│ │                │   │   ),                                                                    │ │
│ │                │   │   GachaLog(                                                             │ │
│ │                │   │   │   cardPoolType='1',                                                 │ │
│ │                │   │   │   resourceId=21010013,                                              │ │
│ │                │   │   │   qualityLevel=3,                                                   │ │
│ │                │   │   │   resourceType='武器',                                              │ │
│ │                │   │   │   name='暗夜长刃·玄明',                                             │ │
│ │                │   │   │   count=1,                                                          │ │
│ │                │   │   │   time='2025-07-26 21:12:28'                                        │ │
│ │                │   │   ),                                                                    │ │
│ │                │   │   GachaLog(                                                             │ │
│ │                │   │   │   cardPoolType='1',                                                 │ │
│ │                │   │   │   resourceId=1608,                                                  │ │
│ │                │   │   │   qualityLevel=5,                                                   │ │
│ │                │   │   │   resourceType='角色',                                              │ │
│ │                │   │   │   name='弗洛洛',                                                    │ │
│ │                │   │   │   count=1,                                                          │ │
│ │                │   │   │   time='2025-07-26 21:12:28'                                        │ │
│ │                │   │   ),                                                                    │ │
│ │                │   │   GachaLog(                                                             │ │
│ │                │   │   │   cardPoolType='1',                                                 │ │
│ │                │   │   │   resourceId=21050043,                                              │ │
│ │                │   │   │   qualityLevel=3,                                                   │ │
│ │                │   │   │   resourceType='武器',                                              │ │
│ │                │   │   │   name='远行者矩阵·探幽',                                           │ │
│ │                │   │   │   count=1,                                                          │ │
│ │                │   │   │   time='2025-07-26 21:12:28'                                        │ │
│ │                │   │   ),                                                                    │ │
│ │                │   │   ... +1365                                                             │ │
│ │                │   ],                                                                        │ │
│ │                │   '武器精准调谐': [                                                         │ │
│ │                │   │   GachaLog(                                                             │ │
│ │                │   │   │   cardPoolType='2',                                                 │ │
│ │                │   │   │   resourceId=21020013,                                              │ │
│ │                │   │   │   qualityLevel=3,                                                   │ │
│ │                │   │   │   resourceType='武器',                                              │ │
│ │                │   │   │   name='暗夜迅刀·黑闪',                                             │ │
│ │                │   │   │   count=1,                                                          │ │
│ │                │   │   │   time='2025-07-30 15:08:41'                                        │ │
│ │                │   │   ),                                                                    │ │
│ │                │   │   GachaLog(                                                             │ │
│ │                │   │   │   cardPoolType='2',                                                 │ │
│ │                │   │   │   resourceId=21010013,                                              │ │
│ │                │   │   │   qualityLevel=3,                                                   │ │
│ │                │   │   │   resourceType='武器',                                              │ │
│ │                │   │   │   name='暗夜长刃·玄明',                                             │ │
│ │                │   │   │   count=1,                                                          │ │
│ │                │   │   │   time='2025-07-30 15:06:23'                                        │ │
│ │                │   │   ),                                                                    │ │
│ │                │   │   GachaLog(                                                             │ │
│ │                │   │   │   cardPoolType='2',                                                 │ │
│ │                │   │   │   resourceId=21010013,                                              │ │
│ │                │   │   │   qualityLevel=3,                                                   │ │
│ │                │   │   │   resourceType='武器',                                              │ │
│ │                │   │   │   name='暗夜长刃·玄明',                                             │ │
│ │                │   │   │   count=1,                                                          │ │
│ │                │   │   │   time='2025-07-30 15:00:35'                                        │ │
│ │                │   │   ),                                                                    │ │
│ │                │   │   GachaLog(                                                             │ │
│ │                │   │   │   cardPoolType='2',                                                 │ │
│ │                │   │   │   resourceId=21050013,                                              │ │
│ │                │   │   │   qualityLevel=3,                                                   │ │
│ │                │   │   │   resourceType='武器',                                              │ │
│ │                │   │   │   name='暗夜矩阵·暝光',                                             │ │
│ │                │   │   │   count=1,                                                          │ │
│ │                │   │   │   time='2025-07-30 14:33:01'                                        │ │
│ │                │   │   ),                                                                    │ │
│ │                │   │   GachaLog(                                                             │ │
│ │                │   │   │   cardPoolType='2',                                                 │ │
│ │                │   │   │   resourceId=21020013,                                              │ │
│ │                │   │   │   qualityLevel=3,                                                   │ │
│ │                │   │   │   resourceType='武器',                                              │ │
│ │                │   │   │   name='暗夜迅刀·黑闪',                                             │ │
│ │                │   │   │   count=1,                                                          │ │
│ │                │   │   │   time='2025-07-30 14:25:36'                                        │ │
│ │                │   │   ),                                                                    │ │
│ │                │   │   GachaLog(                                                             │ │
│ │                │   │   │   cardPoolType='2',                                                 │ │
│ │                │   │   │   resourceId=21020023,                                              │ │
│ │                │   │   │   qualityLevel=3,                                                   │ │
│ │                │   │   │   resourceType='武器',                                              │ │
│ │                │   │   │   name='源能迅刀·测贰',                                             │ │
│ │                │   │   │   count=1,                                                          │ │
│ │                │   │   │   time='2025-07-30 14:25:28'                                        │ │
│ │                │   │   ),                                                                    │ │
│ │                │   │   GachaLog(                                                             │ │
│ │                │   │   │   cardPoolType='2',                                                 │ │
│ │                │   │   │   resourceId=21020064,                                              │ │
│ │                │   │   │   qualityLevel=4,                                                   │ │
│ │                │   │   │   resourceType='武器',                                              │ │
│ │                │   │   │   name='西升',                                                      │ │
│ │                │   │   │   count=1,                                                          │ │
│ │                │   │   │   time='2025-07-30 14:25:19'                                        │ │
│ │                │   │   ),                                                                    │ │
│ │                │   │   GachaLog(                                                             │ │
│ │                │   │   │   cardPoolType='2',                                                 │ │
│ │                │   │   │   resourceId=21010023,                                              │ │
│ │                │   │   │   qualityLevel=3,                                                   │ │
│ │                │   │   │   resourceType='武器',                                              │ │
│ │                │   │   │   name='源能长刃·测壹',                                             │ │
│ │                │   │   │   count=1,                                                          │ │
│ │                │   │   │   time='2025-07-30 14:25:11'                                        │ │
│ │                │   │   ),                                                                    │ │
│ │                │   │   GachaLog(                                                             │ │
│ │                │   │   │   cardPoolType='2',                                                 │ │
│ │                │   │   │   resourceId=21030043,                                              │ │
│ │                │   │   │   qualityLevel=3,                                                   │ │
│ │                │   │   │   resourceType='武器',                                              │ │
│ │                │   │   │   name='远行者佩枪·洞察',                                           │ │
│ │                │   │   │   count=1,                                                          │ │
│ │                │   │   │   time='2025-07-30 14:25:03'                                        │ │
│ │                │   │   ),                                                                    │ │
│ │                │   │   GachaLog(                                                             │ │
│ │                │   │   │   cardPoolType='2',                                                 │ │
│ │                │   │   │   resourceId=21040013,                                              │ │
│ │                │   │   │   qualityLevel=3,                                                   │ │
│ │                │   │   │   resourceType='武器',                                              │ │
│ │                │   │   │   name='暗夜臂铠·夜芒',                                             │ │
│ │                │   │   │   count=1,                                                          │ │
│ │                │   │   │   time='2025-07-26 21:31:21'                                        │ │
│ │                │   │   ),                                                                    │ │
│ │                │   │   ... +689                                                              │ │
│ │                │   ],                                                                        │ │
│ │                │   '角色调谐（常驻池）': [                                                   │ │
│ │                │   │   GachaLog(                                                             │ │
│ │                │   │   │   cardPoolType='3',                                                 │ │
│ │                │   │   │   resourceId=21010023,                                              │ │
│ │                │   │   │   qualityLevel=3,                                                   │ │
│ │                │   │   │   resourceType='武器',                                              │ │
│ │                │   │   │   name='源能长刃·测壹',                                             │ │
│ │                │   │   │   count=1,                                                          │ │
│ │                │   │   │   time='2025-07-24 09:34:55'                                        │ │
│ │                │   │   ),                                                                    │ │
│ │                │   │   GachaLog(                                                             │ │
│ │                │   │   │   cardPoolType='3',                                                 │ │
│ │                │   │   │   resourceId=21050043,                                              │ │
│ │                │   │   │   qualityLevel=3,                                                   │ │
│ │                │   │   │   resourceType='武器',                                              │ │
│ │                │   │   │   name='远行者矩阵·探幽',                                           │ │
│ │                │   │   │   count=1,                                                          │ │
│ │                │   │   │   time='2025-07-24 09:34:55'                                        │ │
│ │                │   │   ),                                                                    │ │
│ │                │   │   GachaLog(                                                             │ │
│ │                │   │   │   cardPoolType='3',                                                 │ │
│ │                │   │   │   resourceId=1403,                                                  │ │
│ │                │   │   │   qualityLevel=4,                                                   │ │
│ │                │   │   │   resourceType='角色',                                              │ │
│ │                │   │   │   name='秋水',                                                      │ │
│ │                │   │   │   count=1,                                                          │ │
│ │                │   │   │   time='2025-07-24 09:34:55'                                        │ │
│ │                │   │   ),                                                                    │ │
│ │                │   │   GachaLog(                                                             │ │
│ │                │   │   │   cardPoolType='3',                                                 │ │
│ │                │   │   │   resourceId=21050023,                                              │ │
│ │                │   │   │   qualityLevel=3,                                                   │ │
│ │                │   │   │   resourceType='武器',                                              │ │
│ │                │   │   │   name='源能音感仪·测五',                                           │ │
│ │                │   │   │   count=1,                                                          │ │
│ │                │   │   │   time='2025-07-24 09:34:55'                                        │ │
│ │                │   │   ),                                                                    │ │
│ │                │   │   GachaLog(                                                             │ │
│ │                │   │   │   cardPoolType='3',                                                 │ │
│ │                │   │   │   resourceId=21040023,                                              │ │
│ │                │   │   │   qualityLevel=3,                                                   │ │
│ │                │   │   │   resourceType='武器',                                              │ │
│ │                │   │   │   name='源能臂铠·测肆',                                             │ │
│ │                │   │   │   count=1,                                                          │ │
│ │                │   │   │   time='2025-07-24 09:34:55'                                        │ │
│ │                │   │   ),                                                                    │ │
│ │                │   │   GachaLog(                                                             │ │
│ │                │   │   │   cardPoolType='3',                                                 │ │
│ │                │   │   │   resourceId=21050043,                                              │ │
│ │                │   │   │   qualityLevel=3,                                                   │ │
│ │                │   │   │   resourceType='武器',                                              │ │
│ │                │   │   │   name='远行者矩阵·探幽',                                           │ │
│ │                │   │   │   count=1,                                                          │ │
│ │                │   │   │   time='2025-07-24 09:34:55'                                        │ │
│ │                │   │   ),                                                                    │ │
│ │                │   │   GachaLog(                                                             │ │
│ │                │   │   │   cardPoolType='3',                                                 │ │
│ │                │   │   │   resourceId=21050023,                                              │ │
│ │                │   │   │   qualityLevel=3,                                                   │ │
│ │                │   │   │   resourceType='武器',                                              │ │
│ │                │   │   │   name='源能音感仪·测五',                                           │ │
│ │                │   │   │   count=1,                                                          │ │
│ │                │   │   │   time='2025-07-24 09:34:55'                                        │ │
│ │                │   │   ),                                                                    │ │
│ │                │   │   GachaLog(                                                             │ │
│ │                │   │   │   cardPoolType='3',                                                 │ │
│ │                │   │   │   resourceId=21010023,                                              │ │
│ │                │   │   │   qualityLevel=3,                                                   │ │
│ │                │   │   │   resourceType='武器',                                              │ │
│ │                │   │   │   name='源能长刃·测壹',                                             │ │
│ │                │   │   │   count=1,                                                          │ │
│ │                │   │   │   time='2025-07-24 09:34:55'                                        │ │
│ │                │   │   ),                                                                    │ │
│ │                │   │   GachaLog(                                                             │ │
│ │                │   │   │   cardPoolType='3',                                                 │ │
│ │                │   │   │   resourceId=21010023,                                              │ │
│ │                │   │   │   qualityLevel=3,                                                   │ │
│ │                │   │   │   resourceType='武器',                                              │ │
│ │                │   │   │   name='源能长刃·测壹',                                             │ │
│ │                │   │   │   count=1,                                                          │ │
│ │                │   │   │   time='2025-07-24 09:34:55'                                        │ │
│ │                │   │   ),                                                                    │ │
│ │                │   │   GachaLog(                                                             │ │
│ │                │   │   │   cardPoolType='3',                                                 │ │
│ │                │   │   │   resourceId=21050023,                                              │ │
│ │                │   │   │   qualityLevel=3,                                                   │ │
│ │                │   │   │   resourceType='武器',                                              │ │
│ │                │   │   │   name='源能音感仪·测五',                                           │ │
│ │                │   │   │   count=1,                                                          │ │
│ │                │   │   │   time='2025-07-24 09:34:55'                                        │ │
│ │                │   │   ),                                                                    │ │
│ │                │   │   ... +47                                                               │ │
│ │                │   ],                                                                        │ │
│ │                │   '武器调谐（常驻池）': [                                                   │ │
│ │                │   │   GachaLog(                                                             │ │
│ │                │   │   │   cardPoolType='4',                                                 │ │
│ │                │   │   │   resourceId=21020023,                                              │ │
│ │                │   │   │   qualityLevel=3,                                                   │ │
│ │                │   │   │   resourceType='武器',                                              │ │
│ │                │   │   │   name='源能迅刀·测贰',                                             │ │
│ │                │   │   │   count=1,                                                          │ │
│ │                │   │   │   time='2025-07-06 19:25:41'                                        │ │
│ │                │   │   ),                                                                    │ │
│ │                │   │   GachaLog(                                                             │ │
│ │                │   │   │   cardPoolType='4',                                                 │ │
│ │                │   │   │   resourceId=21050043,                                              │ │
│ │                │   │   │   qualityLevel=3,                                                   │ │
│ │                │   │   │   resourceType='武器',                                              │ │
│ │                │   │   │   name='远行者矩阵·探幽',                                           │ │
│ │                │   │   │   count=1,                                                          │ │
│ │                │   │   │   time='2025-07-06 19:25:41'                                        │ │
│ │                │   │   ),                                                                    │ │
│ │                │   │   GachaLog(                                                             │ │
│ │                │   │   │   cardPoolType='4',                                                 │ │
│ │                │   │   │   resourceId=21040023,                                              │ │
│ │                │   │   │   qualityLevel=3,                                                   │ │
│ │                │   │   │   resourceType='武器',                                              │ │
│ │                │   │   │   name='源能臂铠·测肆',                                             │ │
│ │                │   │   │   count=1,                                                          │ │
│ │                │   │   │   time='2025-07-06 19:25:41'                                        │ │
│ │                │   │   ),                                                                    │ │
│ │                │   │   GachaLog(                                                             │ │
│ │                │   │   │   cardPoolType='4',                                                 │ │
│ │                │   │   │   resourceId=21030043,                                              │ │
│ │                │   │   │   qualityLevel=3,                                                   │ │
│ │                │   │   │   resourceType='武器',                                              │ │
│ │                │   │   │   name='远行者佩枪·洞察',                                           │ │
│ │                │   │   │   count=1,                                                          │ │
│ │                │   │   │   time='2025-07-06 19:25:41'                                        │ │
│ │                │   │   ),                                                                    │ │
│ │                │   │   GachaLog(                                                             │ │
│ │                │   │   │   cardPoolType='4',                                                 │ │
│ │                │   │   │   resourceId=21050043,                                              │ │
│ │                │   │   │   qualityLevel=3,                                                   │ │
│ │                │   │   │   resourceType='武器',                                              │ │
│ │                │   │   │   name='远行者矩阵·探幽',                                           │ │
│ │                │   │   │   count=1,                                                          │ │
│ │                │   │   │   time='2025-07-06 19:25:41'                                        │ │
│ │                │   │   ),                                                                    │ │
│ │                │   │   GachaLog(                                                             │ │
│ │                │   │   │   cardPoolType='4',                                                 │ │
│ │                │   │   │   resourceId=1402,                                                  │ │
│ │                │   │   │   qualityLevel=4,                                                   │ │
│ │                │   │   │   resourceType='角色',                                              │ │
│ │                │   │   │   name='秧秧',                                                      │ │
│ │                │   │   │   count=1,                                                          │ │
│ │                │   │   │   time='2025-07-06 19:25:41'                                        │ │
│ │                │   │   ),                                                                    │ │
│ │                │   │   GachaLog(                                                             │ │
│ │                │   │   │   cardPoolType='4',                                                 │ │
│ │                │   │   │   resourceId=21050013,                                              │ │
│ │                │   │   │   qualityLevel=3,                                                   │ │
│ │                │   │   │   resourceType='武器',                                              │ │
│ │                │   │   │   name='暗夜矩阵·暝光',                                             │ │
│ │                │   │   │   count=1,                                                          │ │
│ │                │   │   │   time='2025-07-06 19:25:41'                                        │ │
│ │                │   │   ),                                                                    │ │
│ │                │   │   GachaLog(                                                             │ │
│ │                │   │   │   cardPoolType='4',                                                 │ │
│ │                │   │   │   resourceId=21040013,                                              │ │
│ │                │   │   │   qualityLevel=3,                                                   │ │
│ │                │   │   │   resourceType='武器',                                              │ │
│ │                │   │   │   name='暗夜臂铠·夜芒',                                             │ │
│ │                │   │   │   count=1,                                                          │ │
│ │                │   │   │   time='2025-07-06 19:25:41'                                        │ │
│ │                │   │   ),                                                                    │ │
│ │                │   │   GachaLog(                                                             │ │
│ │                │   │   │   cardPoolType='4',                                                 │ │
│ │                │   │   │   resourceId=21030043,                                              │ │
│ │                │   │   │   qualityLevel=3,                                                   │ │
│ │                │   │   │   resourceType='武器',                                              │ │
│ │                │   │   │   name='远行者佩枪·洞察',                                           │ │
│ │                │   │   │   count=1,                                                          │ │
│ │                │   │   │   time='2025-07-06 19:25:41'                                        │ │
│ │                │   │   ),                                                                    │ │
│ │                │   │   GachaLog(                                                             │ │
│ │                │   │   │   cardPoolType='4',                                                 │ │
│ │                │   │   │   resourceId=21020023,                                              │ │
│ │                │   │   │   qualityLevel=3,                                                   │ │
│ │                │   │   │   resourceType='武器',                                              │ │
│ │                │   │   │   name='源能迅刀·测贰',                                             │ │
│ │                │   │   │   count=1,                                                          │ │
│ │                │   │   │   time='2025-07-06 19:25:41'                                        │ │
│ │                │   │   ),                                                                    │ │
│ │                │   │   ... +254                                                              │ │
│ │                │   ],                                                                        │ │
│ │                │   '新手调谐': [],                                                           │ │
│ │                │   '新手自选唤取': [],                                                       │ │
│ │                │   '新手自选唤取（感恩定向唤取）': [],                                       │ │
│ │                │   '角色新旅唤取': [],                                                       │ │
│ │                │   '武器新旅唤取': []                                                        │ │
│ │                }                                                                             │ │
│ │         item = WWUIDGachaItem(                                                               │ │
│ │                │   cardPoolType='4',                                                         │ │
│ │                │   resourceId=21050023,                                                      │ │
│ │                │   qualityLevel=3,                                                           │ │
│ │                │   resourceType='武器',                                                      │ │
│ │                │   name='源能音感仪·测五',                                                   │ │
│ │                │   count=1,                                                                  │ │
│ │                │   time='2024-05-29 22:16:16'                                                │ │
│ │                )                                                                             │ │
│ │         type = 'base64'                                                                      │ │
│ │          uid = '100381247'                                                                   │ │
│ │  wwuid_gacha = WWUIDGacha(                                                                   │ │
│ │                │   info=WWUIDGachaInfo(                                                      │ │
│ │                │   │   export_time='2025-08-01 22:18:50',                                    │ │
│ │                │   │   export_app='WutheringWavesUID',                                       │ │
│ │                │   │   export_app_version='2.5.0',                                           │ │
│ │                │   │   export_timestamp=1754057930,                                          │ │
│ │                │   │   version='v2.0',                                                       │ │
│ │                │   │   uid='100381247'                                                       │ │
│ │                │   ),                                                                        │ │
│ │                │   list=[                                                                    │ │
│ │                │   │   WWUIDGachaItem(                                                       │ │
│ │                │   │   │   cardPoolType='1',                                                 │ │
│ │                │   │   │   resourceId=21030013,                                              │ │
│ │                │   │   │   qualityLevel=3,                                                   │ │
│ │                │   │   │   resourceType='武器',                                              │ │
│ │                │   │   │   name='暗夜佩枪·暗星',                                             │ │
│ │                │   │   │   count=1,                                                          │ │
│ │                │   │   │   time='2025-07-26 21:12:28'                                        │ │
│ │                │   │   ),                                                                    │ │
│ │                │   │   WWUIDGachaItem(                                                       │ │
│ │                │   │   │   cardPoolType='1',                                                 │ │
│ │                │   │   │   resourceId=21030023,                                              │ │
│ │                │   │   │   qualityLevel=3,                                                   │ │
│ │                │   │   │   resourceType='武器',                                              │ │
│ │                │   │   │   name='源能佩枪·测叁',                                             │ │
│ │                │   │   │   count=1,                                                          │ │
│ │                │   │   │   time='2025-07-26 21:12:28'                                        │ │
│ │                │   │   ),                                                                    │ │
│ │                │   │   WWUIDGachaItem(                                                       │ │
│ │                │   │   │   cardPoolType='1',                                                 │ │
│ │                │   │   │   resourceId=21010013,                                              │ │
│ │                │   │   │   qualityLevel=3,                                                   │ │
│ │                │   │   │   resourceType='武器',                                              │ │
│ │                │   │   │   name='暗夜长刃·玄明',                                             │ │
│ │                │   │   │   count=1,                                                          │ │
│ │                │   │   │   time='2025-07-26 21:12:28'                                        │ │
│ │                │   │   ),                                                                    │ │
│ │                │   │   WWUIDGachaItem(                                                       │ │
│ │                │   │   │   cardPoolType='1',                                                 │ │
│ │                │   │   │   resourceId=21020013,                                              │ │
│ │                │   │   │   qualityLevel=3,                                                   │ │
│ │                │   │   │   resourceType='武器',                                              │ │
│ │                │   │   │   name='暗夜迅刀·黑闪',                                             │ │
│ │                │   │   │   count=1,                                                          │ │
│ │                │   │   │   time='2025-07-26 21:12:28'                                        │ │
│ │                │   │   ),                                                                    │ │
│ │                │   │   WWUIDGachaItem(                                                       │ │
│ │                │   │   │   cardPoolType='1',                                                 │ │
│ │                │   │   │   resourceId=21040013,                                              │ │
│ │                │   │   │   qualityLevel=3,                                                   │ │
│ │                │   │   │   resourceType='武器',                                              │ │
│ │                │   │   │   name='暗夜臂铠·夜芒',                                             │ │
│ │                │   │   │   count=1,                                                          │ │
│ │                │   │   │   time='2025-07-26 21:12:28'                                        │ │
│ │                │   │   ),                                                                    │ │
│ │                │   │   WWUIDGachaItem(                                                       │ │
│ │                │   │   │   cardPoolType='1',                                                 │ │
│ │                │   │   │   resourceId=21010013,                                              │ │
│ │                │   │   │   qualityLevel=3,                                                   │ │
│ │                │   │   │   resourceType='武器',                                              │ │
│ │                │   │   │   name='暗夜长刃·玄明',                                             │ │
│ │                │   │   │   count=1,                                                          │ │
│ │                │   │   │   time='2025-07-26 21:12:28'                                        │ │
│ │                │   │   ),                                                                    │ │
│ │                │   │   WWUIDGachaItem(                                                       │ │
│ │                │   │   │   cardPoolType='1',                                                 │ │
│ │                │   │   │   resourceId=21020023,                                              │ │
│ │                │   │   │   qualityLevel=3,                                                   │ │
│ │                │   │   │   resourceType='武器',                                              │ │
│ │                │   │   │   name='源能迅刀·测贰',                                             │ │
│ │                │   │   │   count=1,                                                          │ │
│ │                │   │   │   time='2025-07-26 21:12:28'                                        │ │
│ │                │   │   ),                                                                    │ │
│ │                │   │   WWUIDGachaItem(                                                       │ │
│ │                │   │   │   cardPoolType='1',                                                 │ │
│ │                │   │   │   resourceId=21010013,                                              │ │
│ │                │   │   │   qualityLevel=3,                                                   │ │
│ │                │   │   │   resourceType='武器',                                              │ │
│ │                │   │   │   name='暗夜长刃·玄明',                                             │ │
│ │                │   │   │   count=1,                                                          │ │
│ │                │   │   │   time='2025-07-26 21:12:28'                                        │ │
│ │                │   │   ),                                                                    │ │
│ │                │   │   WWUIDGachaItem(                                                       │ │
│ │                │   │   │   cardPoolType='1',                                                 │ │
│ │                │   │   │   resourceId=1608,                                                  │ │
│ │                │   │   │   qualityLevel=5,                                                   │ │
│ │                │   │   │   resourceType='角色',                                              │ │
│ │                │   │   │   name='弗洛洛',                                                    │ │
│ │                │   │   │   count=1,                                                          │ │
│ │                │   │   │   time='2025-07-26 21:12:28'                                        │ │
│ │                │   │   ),                                                                    │ │
│ │                │   │   WWUIDGachaItem(                                                       │ │
│ │                │   │   │   cardPoolType='1',                                                 │ │
│ │                │   │   │   resourceId=21050043,                                              │ │
│ │                │   │   │   qualityLevel=3,                                                   │ │
│ │                │   │   │   resourceType='武器',                                              │ │
│ │                │   │   │   name='远行者矩阵·探幽',                                           │ │
│ │                │   │   │   count=1,                                                          │ │
│ │                │   │   │   time='2025-07-26 21:12:28'                                        │ │
│ │                │   │   ),                                                                    │ │
│ │                │   │   ... +2385                                                             │ │
│ │                │   ]                                                                         │ │
│ │                )                                                                             │ │
│ ╰──────────────────────────────────────────────────────────────────────────────────────────────╯ │
│                                                                                                  │
│ /root/gsuid_core/gsuid_core/plugins/WutheringWavesUID/WutheringWavesUID/wutheringwaves_gachalog/ │
│ get_gachalogs.py:193 in save_gachalogs                                                           │
│                                                                                                  │
│   190 │   temp_gachalogs_history = {}                                                            │
│   191 │   if gachalogs_path.exists():                                                            │
│   192 │   │   with Path.open(gachalogs_path, encoding="UTF-8") as f:                             │
│ ❱ 193 │   │   │   gachalogs_history: Dict = json.load(f)                                         │
│   194 │   │                                                                                      │
│   195 │   │   # import 时备份                                                                    │
│   196 │   │   if not record_id:                                                                  │
│                                                                                                  │
│ ╭─────────────────────────────────────────── locals ───────────────────────────────────────────╮ │
│ │                     ev = Event(                                                              │ │
│ │                          │   bot_id='qqgroup',                                               │ │
│ │                          │   bot_self_id='3889000318',                                       │ │
│ │                          │                                                                   │ │
│ │                          msg_id='ROBOT1.0_y1dvI-hJ.hzZd.txFtuXuP9BlGhbg5zfU2dQ3q.9dZf91tK5d… │ │
│ │                          │   user_type='direct',                                             │ │
│ │                          │   group_id=None,                                                  │ │
│ │                          │   user_id='3889000318:4B8A766C5CA11917527B3FCD14830714',          │ │
│ │                          │   sender={                                                        │ │
│ │                          │   │   'user_id': '3889000318:4B8A766C5CA11917527B3FCD14830714',   │ │
│ │                          │   │   'avatar':                                                   │ │
│ │                          'https://q.qlogo.cn/qqapp/102072241/4B8A766C5CA11917527B3FCD148307… │ │
│ │                          │   },                                                              │ │
│ │                          │   user_pm=0,                                                      │ │
│ │                          │   content=[                                                       │ │
│ │                          │   │   Message(                                                    │ │
│ │                          │   │   │   type='file',                                            │ │
│ │                          │   │   │                                                           │ │
│ │                          data='export_100381247.json|eyJpbmZvIjp7ImV4cG9ydF90aW1lIjoiMjAyNS… │ │
│ │                          │   │   )                                                           │ │
│ │                          │   ],                                                              │ │
│ │                          │   task_id='bed9911d-62df-4d11-bba8-465df79f13d1',                 │ │
│ │                          │   task_event=None,                                                │ │
│ │                          │   real_bot_id='qqgroup',                                          │ │
│ │                          │   raw_text='',                                                    │ │
│ │                          │   command='json',                                                 │ │
│ │                          │   text='',                                                        │ │
│ │                          │   image=None,                                                     │ │
│ │                          │   at=None,                                                        │ │
│ │                          │   image_list=[],                                                  │ │
│ │                          │   at_list=[],                                                     │ │
│ │                          │   is_tome=False,                                                  │ │
│ │                          │   reply=None,                                                     │ │
│ │                          │   file_name='export_100381247.json',                              │ │
│ │                          │                                                                   │ │
│ │                          file='eyJpbmZvIjp7ImV4cG9ydF90aW1lIjoiMjAyNS0wOC0wMSAyMjoxODo1MCIs… │ │
│ │                          │   file_type='base64',                                             │ │
│ │                          │   regex_group=(),                                                 │ │
│ │                          │   regex_dict={}                                                   │ │
│ │                          )                                                                   │ │
│ │                      f = <_io.TextIOWrapper                                                  │ │
│ │                          name='/root/gsuid_core/data/WutheringWavesUID/players/100381247/ga… │ │
│ │                          mode='r' encoding='UTF-8'>                                          │ │
│ │         gachalogs_path = PosixPath('/root/gsuid_core/data/WutheringWavesUID/players/1003812… │ │
│ │            import_data = {                                                                   │ │
│ │                          │   '角色精准调谐': [                                               │ │
│ │                          │   │   GachaLog(                                                   │ │
│ │                          │   │   │   cardPoolType='1',                                       │ │
│ │                          │   │   │   resourceId=21030013,                                    │ │
│ │                          │   │   │   qualityLevel=3,                                         │ │
│ │                          │   │   │   resourceType='武器',                                    │ │
│ │                          │   │   │   name='暗夜佩枪·暗星',                                   │ │
│ │                          │   │   │   count=1,                                                │ │
│ │                          │   │   │   time='2025-07-26 21:12:28'                              │ │
│ │                          │   │   ),                                                          │ │
│ │                          │   │   GachaLog(                                                   │ │
│ │                          │   │   │   cardPoolType='1',                                       │ │
│ │                          │   │   │   resourceId=21030023,                                    │ │
│ │                          │   │   │   qualityLevel=3,                                         │ │
│ │                          │   │   │   resourceType='武器',                                    │ │
│ │                          │   │   │   name='源能佩枪·测叁',                                   │ │
│ │                          │   │   │   count=1,                                                │ │
│ │                          │   │   │   time='2025-07-26 21:12:28'                              │ │
│ │                          │   │   ),                                                          │ │
│ │                          │   │   GachaLog(                                                   │ │
│ │                          │   │   │   cardPoolType='1',                                       │ │
│ │                          │   │   │   resourceId=21010013,                                    │ │
│ │                          │   │   │   qualityLevel=3,                                         │ │
│ │                          │   │   │   resourceType='武器',                                    │ │
│ │                          │   │   │   name='暗夜长刃·玄明',                                   │ │
│ │                          │   │   │   count=1,                                                │ │
│ │                          │   │   │   time='2025-07-26 21:12:28'                              │ │
│ │                          │   │   ),                                                          │ │
│ │                          │   │   GachaLog(                                                   │ │
│ │                          │   │   │   cardPoolType='1',                                       │ │
│ │                          │   │   │   resourceId=21020013,                                    │ │
│ │                          │   │   │   qualityLevel=3,                                         │ │
│ │                          │   │   │   resourceType='武器',                                    │ │
│ │                          │   │   │   name='暗夜迅刀·黑闪',                                   │ │
│ │                          │   │   │   count=1,                                                │ │
│ │                          │   │   │   time='2025-07-26 21:12:28'                              │ │
│ │                          │   │   ),                                                          │ │
│ │                          │   │   GachaLog(                                                   │ │
│ │                          │   │   │   cardPoolType='1',                                       │ │
│ │                          │   │   │   resourceId=21040013,                                    │ │
│ │                          │   │   │   qualityLevel=3,                                         │ │
│ │                          │   │   │   resourceType='武器',                                    │ │
│ │                          │   │   │   name='暗夜臂铠·夜芒',                                   │ │
│ │                          │   │   │   count=1,                                                │ │
│ │                          │   │   │   time='2025-07-26 21:12:28'                              │ │
│ │                          │   │   ),                                                          │ │
│ │                          │   │   GachaLog(                                                   │ │
│ │                          │   │   │   cardPoolType='1',                                       │ │
│ │                          │   │   │   resourceId=21010013,                                    │ │
│ │                          │   │   │   qualityLevel=3,                                         │ │
│ │                          │   │   │   resourceType='武器',                                    │ │
│ │                          │   │   │   name='暗夜长刃·玄明',                                   │ │
│ │                          │   │   │   count=1,                                                │ │
│ │                          │   │   │   time='2025-07-26 21:12:28'                              │ │
│ │                          │   │   ),                                                          │ │
│ │                          │   │   GachaLog(                                                   │ │
│ │                          │   │   │   cardPoolType='1',                                       │ │
│ │                          │   │   │   resourceId=21020023,                                    │ │
│ │                          │   │   │   qualityLevel=3,                                         │ │
│ │                          │   │   │   resourceType='武器',                                    │ │
│ │                          │   │   │   name='源能迅刀·测贰',                                   │ │
│ │                          │   │   │   count=1,                                                │ │
│ │                          │   │   │   time='2025-07-26 21:12:28'                              │ │
│ │                          │   │   ),                                                          │ │
│ │                          │   │   GachaLog(                                                   │ │
│ │                          │   │   │   cardPoolType='1',                                       │ │
│ │                          │   │   │   resourceId=21010013,                                    │ │
│ │                          │   │   │   qualityLevel=3,                                         │ │
│ │                          │   │   │   resourceType='武器',                                    │ │
│ │                          │   │   │   name='暗夜长刃·玄明',                                   │ │
│ │                          │   │   │   count=1,                                                │ │
│ │                          │   │   │   time='2025-07-26 21:12:28'                              │ │
│ │                          │   │   ),                                                          │ │
│ │                          │   │   GachaLog(                                                   │ │
│ │                          │   │   │   cardPoolType='1',                                       │ │
│ │                          │   │   │   resourceId=1608,                                        │ │
│ │                          │   │   │   qualityLevel=5,                                         │ │
│ │                          │   │   │   resourceType='角色',                                    │ │
│ │                          │   │   │   name='弗洛洛',                                          │ │
│ │                          │   │   │   count=1,                                                │ │
│ │                          │   │   │   time='2025-07-26 21:12:28'                              │ │
│ │                          │   │   ),                                                          │ │
│ │                          │   │   GachaLog(                                                   │ │
│ │                          │   │   │   cardPoolType='1',                                       │ │
│ │                          │   │   │   resourceId=21050043,                                    │ │
│ │                          │   │   │   qualityLevel=3,                                         │ │
│ │                          │   │   │   resourceType='武器',                                    │ │
│ │                          │   │   │   name='远行者矩阵·探幽',                                 │ │
│ │                          │   │   │   count=1,                                                │ │
│ │                          │   │   │   time='2025-07-26 21:12:28'                              │ │
│ │                          │   │   ),                                                          │ │
│ │                          │   │   ... +1365                                                   │ │
│ │                          │   ],                                                              │ │
│ │                          │   '武器精准调谐': [                                               │ │
│ │                          │   │   GachaLog(                                                   │ │
│ │                          │   │   │   cardPoolType='2',                                       │ │
│ │                          │   │   │   resourceId=21020013,                                    │ │
│ │                          │   │   │   qualityLevel=3,                                         │ │
│ │                          │   │   │   resourceType='武器',                                    │ │
│ │                          │   │   │   name='暗夜迅刀·黑闪',                                   │ │
│ │                          │   │   │   count=1,                                                │ │
│ │                          │   │   │   time='2025-07-30 15:08:41'                              │ │
│ │                          │   │   ),                                                          │ │
│ │                          │   │   GachaLog(                                                   │ │
│ │                          │   │   │   cardPoolType='2',                                       │ │
│ │                          │   │   │   resourceId=21010013,                                    │ │
│ │                          │   │   │   qualityLevel=3,                                         │ │
│ │                          │   │   │   resourceType='武器',                                    │ │
│ │                          │   │   │   name='暗夜长刃·玄明',                                   │ │
│ │                          │   │   │   count=1,                                                │ │
│ │                          │   │   │   time='2025-07-30 15:06:23'                              │ │
│ │                          │   │   ),                                                          │ │
│ │                          │   │   GachaLog(                                                   │ │
│ │                          │   │   │   cardPoolType='2',                                       │ │
│ │                          │   │   │   resourceId=21010013,                                    │ │
│ │                          │   │   │   qualityLevel=3,                                         │ │
│ │                          │   │   │   resourceType='武器',                                    │ │
│ │                          │   │   │   name='暗夜长刃·玄明',                                   │ │
│ │                          │   │   │   count=1,                                                │ │
│ │                          │   │   │   time='2025-07-30 15:00:35'                              │ │
│ │                          │   │   ),                                                          │ │
│ │                          │   │   GachaLog(                                                   │ │
│ │                          │   │   │   cardPoolType='2',                                       │ │
│ │                          │   │   │   resourceId=21050013,                                    │ │
│ │                          │   │   │   qualityLevel=3,                                         │ │
│ │                          │   │   │   resourceType='武器',                                    │ │
│ │                          │   │   │   name='暗夜矩阵·暝光',                                   │ │
│ │                          │   │   │   count=1,                                                │ │
│ │                          │   │   │   time='2025-07-30 14:33:01'                              │ │
│ │                          │   │   ),                                                          │ │
│ │                          │   │   GachaLog(                                                   │ │
│ │                          │   │   │   cardPoolType='2',                                       │ │
│ │                          │   │   │   resourceId=21020013,                                    │ │
│ │                          │   │   │   qualityLevel=3,                                         │ │
│ │                          │   │   │   resourceType='武器',                                    │ │
│ │                          │   │   │   name='暗夜迅刀·黑闪',                                   │ │
│ │                          │   │   │   count=1,                                                │ │
│ │                          │   │   │   time='2025-07-30 14:25:36'                              │ │
│ │                          │   │   ),                                                          │ │
│ │                          │   │   GachaLog(                                                   │ │
│ │                          │   │   │   cardPoolType='2',                                       │ │
│ │                          │   │   │   resourceId=21020023,                                    │ │
│ │                          │   │   │   qualityLevel=3,                                         │ │
│ │                          │   │   │   resourceType='武器',                                    │ │
│ │                          │   │   │   name='源能迅刀·测贰',                                   │ │
│ │                          │   │   │   count=1,                                                │ │
│ │                          │   │   │   time='2025-07-30 14:25:28'                              │ │
│ │                          │   │   ),                                                          │ │
│ │                          │   │   GachaLog(                                                   │ │
│ │                          │   │   │   cardPoolType='2',                                       │ │
│ │                          │   │   │   resourceId=21020064,                                    │ │
│ │                          │   │   │   qualityLevel=4,                                         │ │
│ │                          │   │   │   resourceType='武器',                                    │ │
│ │                          │   │   │   name='西升',                                            │ │
│ │                          │   │   │   count=1,                                                │ │
│ │                          │   │   │   time='2025-07-30 14:25:19'                              │ │
│ │                          │   │   ),                                                          │ │
│ │                          │   │   GachaLog(                                                   │ │
│ │                          │   │   │   cardPoolType='2',                                       │ │
│ │                          │   │   │   resourceId=21010023,                                    │ │
│ │                          │   │   │   qualityLevel=3,                                         │ │
│ │                          │   │   │   resourceType='武器',                                    │ │
│ │                          │   │   │   name='源能长刃·测壹',                                   │ │
│ │                          │   │   │   count=1,                                                │ │
│ │                          │   │   │   time='2025-07-30 14:25:11'                              │ │
│ │                          │   │   ),                                                          │ │
│ │                          │   │   GachaLog(                                                   │ │
│ │                          │   │   │   cardPoolType='2',                                       │ │
│ │                          │   │   │   resourceId=21030043,                                    │ │
│ │                          │   │   │   qualityLevel=3,                                         │ │
│ │                          │   │   │   resourceType='武器',                                    │ │
│ │                          │   │   │   name='远行者佩枪·洞察',                                 │ │
│ │                          │   │   │   count=1,                                                │ │
│ │                          │   │   │   time='2025-07-30 14:25:03'                              │ │
│ │                          │   │   ),                                                          │ │
│ │                          │   │   GachaLog(                                                   │ │
│ │                          │   │   │   cardPoolType='2',                                       │ │
│ │                          │   │   │   resourceId=21040013,                                    │ │
│ │                          │   │   │   qualityLevel=3,                                         │ │
│ │                          │   │   │   resourceType='武器',                                    │ │
│ │                          │   │   │   name='暗夜臂铠·夜芒',                                   │ │
│ │                          │   │   │   count=1,                                                │ │
│ │                          │   │   │   time='2025-07-26 21:31:21'                              │ │
│ │                          │   │   ),                                                          │ │
│ │                          │   │   ... +689                                                    │ │
│ │                          │   ],                                                              │ │
│ │                          │   '角色调谐（常驻池）': [                                         │ │
│ │                          │   │   GachaLog(                                                   │ │
│ │                          │   │   │   cardPoolType='3',                                       │ │
│ │                          │   │   │   resourceId=21010023,                                    │ │
│ │                          │   │   │   qualityLevel=3,                                         │ │
│ │                          │   │   │   resourceType='武器',                                    │ │
│ │                          │   │   │   name='源能长刃·测壹',                                   │ │
│ │                          │   │   │   count=1,                                                │ │
│ │                          │   │   │   time='2025-07-24 09:34:55'                              │ │
│ │                          │   │   ),                                                          │ │
│ │                          │   │   GachaLog(                                                   │ │
│ │                          │   │   │   cardPoolType='3',                                       │ │
│ │                          │   │   │   resourceId=21050043,                                    │ │
│ │                          │   │   │   qualityLevel=3,                                         │ │
│ │                          │   │   │   resourceType='武器',                                    │ │
│ │                          │   │   │   name='远行者矩阵·探幽',                                 │ │
│ │                          │   │   │   count=1,                                                │ │
│ │                          │   │   │   time='2025-07-24 09:34:55'                              │ │
│ │                          │   │   ),                                                          │ │
│ │                          │   │   GachaLog(                                                   │ │
│ │                          │   │   │   cardPoolType='3',                                       │ │
│ │                          │   │   │   resourceId=1403,                                        │ │
│ │                          │   │   │   qualityLevel=4,                                         │ │
│ │                          │   │   │   resourceType='角色',                                    │ │
│ │                          │   │   │   name='秋水',                                            │ │
│ │                          │   │   │   count=1,                                                │ │
│ │                          │   │   │   time='2025-07-24 09:34:55'                              │ │
│ │                          │   │   ),                                                          │ │
│ │                          │   │   GachaLog(                                                   │ │
│ │                          │   │   │   cardPoolType='3',                                       │ │
│ │                          │   │   │   resourceId=21050023,                                    │ │
│ │                          │   │   │   qualityLevel=3,                                         │ │
│ │                          │   │   │   resourceType='武器',                                    │ │
│ │                          │   │   │   name='源能音感仪·测五',                                 │ │
│ │                          │   │   │   count=1,                                                │ │
│ │                          │   │   │   time='2025-07-24 09:34:55'                              │ │
│ │                          │   │   ),                                                          │ │
│ │                          │   │   GachaLog(                                                   │ │
│ │                          │   │   │   cardPoolType='3',                                       │ │
│ │                          │   │   │   resourceId=21040023,                                    │ │
│ │                          │   │   │   qualityLevel=3,                                         │ │
│ │                          │   │   │   resourceType='武器',                                    │ │
│ │                          │   │   │   name='源能臂铠·测肆',                                   │ │
│ │                          │   │   │   count=1,                                                │ │
│ │                          │   │   │   time='2025-07-24 09:34:55'                              │ │
│ │                          │   │   ),                                                          │ │
│ │                          │   │   GachaLog(                                                   │ │
│ │                          │   │   │   cardPoolType='3',                                       │ │
│ │                          │   │   │   resourceId=21050043,                                    │ │
│ │                          │   │   │   qualityLevel=3,                                         │ │
│ │                          │   │   │   resourceType='武器',                                    │ │
│ │                          │   │   │   name='远行者矩阵·探幽',                                 │ │
│ │                          │   │   │   count=1,                                                │ │
│ │                          │   │   │   time='2025-07-24 09:34:55'                              │ │
│ │                          │   │   ),                                                          │ │
│ │                          │   │   GachaLog(                                                   │ │
│ │                          │   │   │   cardPoolType='3',                                       │ │
│ │                          │   │   │   resourceId=21050023,                                    │ │
│ │                          │   │   │   qualityLevel=3,                                         │ │
│ │                          │   │   │   resourceType='武器',                                    │ │
│ │                          │   │   │   name='源能音感仪·测五',                                 │ │
│ │                          │   │   │   count=1,                                                │ │
│ │                          │   │   │   time='2025-07-24 09:34:55'                              │ │
│ │                          │   │   ),                                                          │ │
│ │                          │   │   GachaLog(                                                   │ │
│ │                          │   │   │   cardPoolType='3',                                       │ │
│ │                          │   │   │   resourceId=21010023,                                    │ │
│ │                          │   │   │   qualityLevel=3,                                         │ │
│ │                          │   │   │   resourceType='武器',                                    │ │
│ │                          │   │   │   name='源能长刃·测壹',                                   │ │
│ │                          │   │   │   count=1,                                                │ │
│ │                          │   │   │   time='2025-07-24 09:34:55'                              │ │
│ │                          │   │   ),                                                          │ │
│ │                          │   │   GachaLog(                                                   │ │
│ │                          │   │   │   cardPoolType='3',                                       │ │
│ │                          │   │   │   resourceId=21010023,                                    │ │
│ │                          │   │   │   qualityLevel=3,                                         │ │
│ │                          │   │   │   resourceType='武器',                                    │ │
│ │                          │   │   │   name='源能长刃·测壹',                                   │ │
│ │                          │   │   │   count=1,                                                │ │
│ │                          │   │   │   time='2025-07-24 09:34:55'                              │ │
│ │                          │   │   ),                                                          │ │
│ │                          │   │   GachaLog(                                                   │ │
│ │                          │   │   │   cardPoolType='3',                                       │ │
│ │                          │   │   │   resourceId=21050023,                                    │ │
│ │                          │   │   │   qualityLevel=3,                                         │ │
│ │                          │   │   │   resourceType='武器',                                    │ │
│ │                          │   │   │   name='源能音感仪·测五',                                 │ │
│ │                          │   │   │   count=1,                                                │ │
│ │                          │   │   │   time='2025-07-24 09:34:55'                              │ │
│ │                          │   │   ),                                                          │ │
│ │                          │   │   ... +47                                                     │ │
│ │                          │   ],                                                              │ │
│ │                          │   '武器调谐（常驻池）': [                                         │ │
│ │                          │   │   GachaLog(                                                   │ │
│ │                          │   │   │   cardPoolType='4',                                       │ │
│ │                          │   │   │   resourceId=21020023,                                    │ │
│ │                          │   │   │   qualityLevel=3,                                         │ │
│ │                          │   │   │   resourceType='武器',                                    │ │
│ │                          │   │   │   name='源能迅刀·测贰',                                   │ │
│ │                          │   │   │   count=1,                                                │ │
│ │                          │   │   │   time='2025-07-06 19:25:41'                              │ │
│ │                          │   │   ),                                                          │ │
│ │                          │   │   GachaLog(                                                   │ │
│ │                          │   │   │   cardPoolType='4',                                       │ │
│ │                          │   │   │   resourceId=21050043,                                    │ │
│ │                          │   │   │   qualityLevel=3,                                         │ │
│ │                          │   │   │   resourceType='武器',                                    │ │
│ │                          │   │   │   name='远行者矩阵·探幽',                                 │ │
│ │                          │   │   │   count=1,                                                │ │
│ │                          │   │   │   time='2025-07-06 19:25:41'                              │ │
│ │                          │   │   ),                                                          │ │
│ │                          │   │   GachaLog(                                                   │ │
│ │                          │   │   │   cardPoolType='4',                                       │ │
│ │                          │   │   │   resourceId=21040023,                                    │ │
│ │                          │   │   │   qualityLevel=3,                                         │ │
│ │                          │   │   │   resourceType='武器',                                    │ │
│ │                          │   │   │   name='源能臂铠·测肆',                                   │ │
│ │                          │   │   │   count=1,                                                │ │
│ │                          │   │   │   time='2025-07-06 19:25:41'                              │ │
│ │                          │   │   ),                                                          │ │
│ │                          │   │   GachaLog(                                                   │ │
│ │                          │   │   │   cardPoolType='4',                                       │ │
│ │                          │   │   │   resourceId=21030043,                                    │ │
│ │                          │   │   │   qualityLevel=3,                                         │ │
│ │                          │   │   │   resourceType='武器',                                    │ │
│ │                          │   │   │   name='远行者佩枪·洞察',                                 │ │
│ │                          │   │   │   count=1,                                                │ │
│ │                          │   │   │   time='2025-07-06 19:25:41'                              │ │
│ │                          │   │   ),                                                          │ │
│ │                          │   │   GachaLog(                                                   │ │
│ │                          │   │   │   cardPoolType='4',                                       │ │
│ │                          │   │   │   resourceId=21050043,                                    │ │
│ │                          │   │   │   qualityLevel=3,                                         │ │
│ │                          │   │   │   resourceType='武器',                                    │ │
│ │                          │   │   │   name='远行者矩阵·探幽',                                 │ │
│ │                          │   │   │   count=1,                                                │ │
│ │                          │   │   │   time='2025-07-06 19:25:41'                              │ │
│ │                          │   │   ),                                                          │ │
│ │                          │   │   GachaLog(                                                   │ │
│ │                          │   │   │   cardPoolType='4',                                       │ │
│ │                          │   │   │   resourceId=1402,                                        │ │
│ │                          │   │   │   qualityLevel=4,                                         │ │
│ │                          │   │   │   resourceType='角色',                                    │ │
│ │                          │   │   │   name='秧秧',                                            │ │
│ │                          │   │   │   count=1,                                                │ │
│ │                          │   │   │   time='2025-07-06 19:25:41'                              │ │
│ │                          │   │   ),                                                          │ │
│ │                          │   │   GachaLog(                                                   │ │
│ │                          │   │   │   cardPoolType='4',                                       │ │
│ │                          │   │   │   resourceId=21050013,                                    │ │
│ │                          │   │   │   qualityLevel=3,                                         │ │
│ │                          │   │   │   resourceType='武器',                                    │ │
│ │                          │   │   │   name='暗夜矩阵·暝光',                                   │ │
│ │                          │   │   │   count=1,                                                │ │
│ │                          │   │   │   time='2025-07-06 19:25:41'                              │ │
│ │                          │   │   ),                                                          │ │
│ │                          │   │   GachaLog(                                                   │ │
│ │                          │   │   │   cardPoolType='4',                                       │ │
│ │                          │   │   │   resourceId=21040013,                                    │ │
│ │                          │   │   │   qualityLevel=3,                                         │ │
│ │                          │   │   │   resourceType='武器',                                    │ │
│ │                          │   │   │   name='暗夜臂铠·夜芒',                                   │ │
│ │                          │   │   │   count=1,                                                │ │
│ │                          │   │   │   time='2025-07-06 19:25:41'                              │ │
│ │                          │   │   ),                                                          │ │
│ │                          │   │   GachaLog(                                                   │ │
│ │                          │   │   │   cardPoolType='4',                                       │ │
│ │                          │   │   │   resourceId=21030043,                                    │ │
│ │                          │   │   │   qualityLevel=3,                                         │ │
│ │                          │   │   │   resourceType='武器',                                    │ │
│ │                          │   │   │   name='远行者佩枪·洞察',                                 │ │
│ │                          │   │   │   count=1,                                                │ │
│ │                          │   │   │   time='2025-07-06 19:25:41'                              │ │
│ │                          │   │   ),                                                          │ │
│ │                          │   │   GachaLog(                                                   │ │
│ │                          │   │   │   cardPoolType='4',                                       │ │
│ │                          │   │   │   resourceId=21020023,                                    │ │
│ │                          │   │   │   qualityLevel=3,                                         │ │
│ │                          │   │   │   resourceType='武器',                                    │ │
│ │                          │   │   │   name='源能迅刀·测贰',                                   │ │
│ │                          │   │   │   count=1,                                                │ │
│ │                          │   │   │   time='2025-07-06 19:25:41'                              │ │
│ │                          │   │   ),                                                          │ │
│ │                          │   │   ... +254                                                    │ │
│ │                          │   ],                                                              │ │
│ │                          │   '新手调谐': [],                                                 │ │
│ │                          │   '新手自选唤取': [],                                             │ │
│ │                          │   '新手自选唤取（感恩定向唤取）': [],                             │ │
│ │                          │   '角色新旅唤取': [],                                             │ │
│ │                          │   '武器新旅唤取': []                                              │ │
│ │                          }                                                                   │ │
│ │               is_force = False                                                               │ │
│ │                   path = PosixPath('/root/gsuid_core/data/WutheringWavesUID/players/1003812… │ │
│ │              record_id = ''                                                                  │ │
│ │ temp_gachalogs_history = {}                                                                  │ │
│ │                    uid = '100381247'                                                         │ │
│ ╰──────────────────────────────────────────────────────────────────────────────────────────────╯ │
│                                                                                                  │
│ /root/.local/share/uv/python/cpython-3.10.18-linux-x86_64-gnu/lib/python3.10/json/__init__.py:29 │
│ 3 in load                                                                                        │
│                                                                                                  │
│   290 │   To use a custom ``JSONDecoder`` subclass, specify it with the ``cls``                  │
│   291 │   kwarg; otherwise ``JSONDecoder`` is used.                                              │
│   292 │   """                                                                                    │
│ ❱ 293 │   return loads(fp.read(),                                                                │
│   294 │   │   cls=cls, object_hook=object_hook,                                                  │
│   295 │   │   parse_float=parse_float, parse_int=parse_int,                                      │
│   296 │   │   parse_constant=parse_constant, object_pairs_hook=object_pairs_hook, **kw)          │
│                                                                                                  │
│ ╭─────────────────────────────────────────── locals ───────────────────────────────────────────╮ │
│ │               cls = None                                                                     │ │
│ │                fp = <_io.TextIOWrapper                                                       │ │
│ │                     name='/root/gsuid_core/data/WutheringWavesUID/players/100381247/gacha_l… │ │
│ │                     mode='r' encoding='UTF-8'>                                               │ │
│ │                kw = {}                                                                       │ │
│ │       object_hook = None                                                                     │ │
│ │ object_pairs_hook = None                                                                     │ │
│ │    parse_constant = None                                                                     │ │
│ │       parse_float = None                                                                     │ │
│ │         parse_int = None                                                                     │ │
│ ╰──────────────────────────────────────────────────────────────────────────────────────────────╯ │
│                                                                                                  │
│ /root/.local/share/uv/python/cpython-3.10.18-linux-x86_64-gnu/lib/python3.10/json/__init__.py:34 │
│ 6 in loads                                                                                       │
│                                                                                                  │
│   343 │   if (cls is None and object_hook is None and                                            │
│   344 │   │   │   parse_int is None and parse_float is None and                                  │
│   345 │   │   │   parse_constant is None and object_pairs_hook is None and not kw):              │
│ ❱ 346 │   │   return _default_decoder.decode(s)                                                  │
│   347 │   if cls is None:                                                                        │
│   348 │   │   cls = JSONDecoder                                                                  │
│   349 │   if object_hook is not None:                                                            │
│                                                                                                  │
│ ╭───────── locals ─────────╮                                                                     │
│ │               cls = None │                                                                     │
│ │                kw = {}   │                                                                     │
│ │       object_hook = None │                                                                     │
│ │ object_pairs_hook = None │                                                                     │
│ │    parse_constant = None │                                                                     │
│ │       parse_float = None │                                                                     │
│ │         parse_int = None │                                                                     │
│ │                 s = ''   │                                                                     │
│ ╰──────────────────────────╯                                                                     │
│                                                                                                  │
│ /root/.local/share/uv/python/cpython-3.10.18-linux-x86_64-gnu/lib/python3.10/json/decoder.py:337 │
│ in decode                                                                                        │
│                                                                                                  │
│   334 │   │   containing a JSON document).                                                       │
│   335 │   │                                                                                      │
│   336 │   │   """                                                                                │
│ ❱ 337 │   │   obj, end = self.raw_decode(s, idx=_w(s, 0).end())                                  │
│   338 │   │   end = _w(s, end).end()                                                             │
│   339 │   │   if end != len(s):                                                                  │
│   340 │   │   │   raise JSONDecodeError("Extra data", s, end)                                    │
│                                                                                                  │
│ ╭─────────────────────────────── locals ────────────────────────────────╮                        │
│ │   _w = <built-in method match of re.Pattern object at 0x7f7cf285c6c0> │                        │
│ │    s = ''                                                             │                        │
│ │ self = <json.decoder.JSONDecoder object at 0x7f7cf288dab0>            │                        │
│ ╰───────────────────────────────────────────────────────────────────────╯                        │
│                                                                                                  │
│ /root/.local/share/uv/python/cpython-3.10.18-linux-x86_64-gnu/lib/python3.10/json/decoder.py:355 │
│ in raw_decode                                                                                    │
│                                                                                                  │
│   352 │   │   try:                                                                               │
│   353 │   │   │   obj, end = self.scan_once(s, idx)                                              │
│   354 │   │   except StopIteration as err:                                                       │
│ ❱ 355 │   │   │   raise JSONDecodeError("Expecting value", s, err.value) from None               │
│   356 │   │   return obj, end                                                                    │
│   357                                                                                            │
│                                                                                                  │
│ ╭────────────────────────── locals ──────────────────────────╮                                   │
│ │  idx = 0                                                   │                                   │
│ │    s = ''                                                  │                                   │
│ │ self = <json.decoder.JSONDecoder object at 0x7f7cf288dab0> │                                   │
│ ╰────────────────────────────────────────────────────────────╯                                   │
╰──────────────────────────────────────────────────────────────────────────────────────────────────╯
JSONDecodeError: Expecting value: line 1 column 1 (char 0)
