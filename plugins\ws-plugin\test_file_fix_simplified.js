// 测试简化后的文件发送功能
// 直接测试核心逻辑，不导入复杂的依赖

// 模拟全局变量
global.Bot = {}
global.logger = {
  info: console.log,
  warn: console.warn,
  error: console.error,
}

// 模拟segment
global.segment = {
  file: (buffer, name) => ({ type: "file", buffer, name }),
}

// 简化的文件处理测试函数
async function testFileLogic() {
  console.log("=== 测试简化的文件处理逻辑 ===")

  // 测试1: 群聊文件处理（应该被跳过）
  console.log("\n1. 测试群聊文件处理（应该被跳过）:")
  const isGroup = true
  const isTrss = false
  if (isGroup || isTrss) {
    console.log("✓ 群聊文件处理被正确跳过")
  } else {
    console.log("✗ 群聊文件处理没有被跳过")
  }

  // 测试2: 私聊文件处理
  console.log("\n2. 测试私聊文件处理:")
  const mockFileData = {
    isGroup: false,
    file: { fid: "test123", name: "test.json" },
    friend: {
      getFileUrl: async fid => {
        console.log(`  获取文件URL: ${fid}`)
        return "http://example.com/test.json"
      },
    },
  }

  // 模拟fetch
  global.fetch = async url => {
    console.log(`  下载文件: ${url}`)
    const testData = JSON.stringify({ test: "data" })
    return {
      arrayBuffer: async () => new TextEncoder().encode(testData).buffer,
    }
  }

  try {
    const fileUrl = await mockFileData.friend.getFileUrl(mockFileData.file.fid)
    if (fileUrl) {
      const res = await fetch(fileUrl)
      const arrayBuffer = await res.arrayBuffer()
      const buffer = Buffer.from(arrayBuffer)
      const base64 = buffer.toString("base64")
      const name = mockFileData.file.name

      const fileData = `${name}|${base64}`
      console.log(`  ✓ 文件处理成功: ${name}, base64长度: ${base64.length}`)
      console.log(`  文件数据格式: ${fileData.substring(0, 50)}...`)
    }
  } catch (error) {
    console.log(`  ✗ 文件处理失败: ${error.message}`)
  }

  // 测试3: 文件发送逻辑
  console.log("\n3. 测试文件发送逻辑:")
  const mockSendData = "test.json|eyJ0ZXN0IjoiZGF0YSJ9" // {"test":"data"} in base64
  const fileParts = mockSendData.split("|")

  if (fileParts.length >= 2) {
    const fileName = fileParts[0]
    const base64Data = fileParts[1]
    const buffer = Buffer.from(base64Data, "base64")

    console.log(`  ✓ 文件解析成功: ${fileName}, 大小: ${buffer.length} bytes`)
    console.log(`  解码内容: ${buffer.toString("utf8")}`)

    // 模拟群聊上传
    console.log("  模拟群聊文件上传...")
    console.log(`  ✓ 文件上传模拟成功: ${fileName}`)
  } else {
    console.log("  ✗ 文件数据格式错误")
  }
}

// 运行测试
async function runTests() {
  await testFileLogic()
  console.log("\n=== 测试完成 ===")
}

runTests().catch(console.error)
