// 测试改进后的文件处理和重复消息检查功能

// 模拟全局变量
global.Bot = {}
global.logger = {
  info: console.log,
  warn: console.warn,
  error: console.error,
  mark: console.log
}

global.segment = {
  file: (buffer, name) => ({ type: 'file', buffer, name })
}

global.Version = {
  isTrss: false
}

// 测试重复消息检查逻辑
function testDuplicateMessageLogic() {
  console.log('=== 测试改进的重复消息检查逻辑 ===')
  
  // 模拟消息去重缓存
  const recentMsgIds = new Map()
  
  // 测试函数
  function checkDuplicate(data) {
    if (data.msg_id) {
      const now = Date.now()
      const msgKey = `${data.msg_id}_${data.target_type}_${data.target_id}`
      
      if (recentMsgIds.has(msgKey)) {
        const lastProcessTime = recentMsgIds.get(msgKey)
        if (now - lastProcessTime < 2000) {
          return true // 是重复消息
        }
      }
      
      recentMsgIds.set(msgKey, now)
      return false // 不是重复消息
    }
    return false
  }
  
  // 测试1: 正常消息
  console.log('\n1. 测试正常消息:')
  const msg1 = {
    msg_id: 'test123',
    target_type: 'group',
    target_id: '456789',
    content: [{ type: 'text', data: 'hello' }]
  }
  
  const isDup1 = checkDuplicate(msg1)
  console.log(`  消息1是否重复: ${isDup1} (应该是false)`)
  
  // 测试2: 立即重复的消息
  const isDup2 = checkDuplicate(msg1)
  console.log(`  立即重复的消息1: ${isDup2} (应该是true)`)
  
  // 测试3: 不同内容但相同msg_id的消息（应该被拦截）
  const msg2 = {
    msg_id: 'test123',
    target_type: 'group', 
    target_id: '456789',
    content: [{ type: 'text', data: 'different content' }]
  }
  
  const isDup3 = checkDuplicate(msg2)
  console.log(`  相同msg_id不同内容: ${isDup3} (应该是true)`)
  
  // 测试4: 不同群的相同msg_id（应该不被拦截）
  const msg3 = {
    msg_id: 'test123',
    target_type: 'group',
    target_id: '999999', // 不同的群
    content: [{ type: 'text', data: 'hello' }]
  }
  
  const isDup4 = checkDuplicate(msg3)
  console.log(`  不同群相同msg_id: ${isDup4} (应该是false)`)
  
  // 测试5: 多个图片消息（模拟同时发送两张不同图片）
  console.log('\n2. 测试多图片消息:')
  const imgMsg1 = {
    msg_id: 'img001',
    target_type: 'group',
    target_id: '456789',
    content: [
      { type: 'image', data: 'image1.jpg' },
      { type: 'image', data: 'image2.jpg' }
    ]
  }
  
  const imgMsg2 = {
    msg_id: 'img002', // 不同的msg_id
    target_type: 'group',
    target_id: '456789',
    content: [
      { type: 'image', data: 'image3.jpg' },
      { type: 'image', data: 'image4.jpg' }
    ]
  }
  
  const isDupImg1 = checkDuplicate(imgMsg1)
  const isDupImg2 = checkDuplicate(imgMsg2)
  console.log(`  图片消息1: ${isDupImg1} (应该是false)`)
  console.log(`  图片消息2: ${isDupImg2} (应该是false)`)
}

// 测试文件处理逻辑
async function testFileProcessing() {
  console.log('\n=== 测试改进的文件处理逻辑 ===')
  
  // 模拟fetch
  global.fetch = async (url) => {
    console.log(`  下载文件: ${url}`)
    const testData = JSON.stringify({ test: 'data' })
    return {
      arrayBuffer: async () => new TextEncoder().encode(testData).buffer
    }
  }
  
  // 测试1: 群聊文件处理
  console.log('\n1. 测试群聊文件处理:')
  const groupFileData = {
    isGroup: true,
    file: { fid: 'group123', name: 'group_file.json' },
    group: {
      getFileUrl: async (fid) => {
        console.log(`  获取群文件URL: ${fid}`)
        return 'http://example.com/group_file.json'
      }
    }
  }
  
  try {
    const fileUrl = await groupFileData.group.getFileUrl(groupFileData.file.fid)
    if (fileUrl) {
      const res = await fetch(fileUrl)
      const arrayBuffer = await res.arrayBuffer()
      const buffer = Buffer.from(arrayBuffer)
      const base64 = buffer.toString('base64')
      const name = groupFileData.file.name
      
      console.log(`  ✓ 群文件处理成功: ${name}, base64长度: ${base64.length}`)
    }
  } catch (error) {
    console.log(`  ✗ 群文件处理失败: ${error.message}`)
  }
  
  // 测试2: 私聊文件处理
  console.log('\n2. 测试私聊文件处理:')
  const privateFileData = {
    isGroup: false,
    file: { fid: 'private123', name: 'private_file.json' },
    friend: {
      getFileUrl: async (fid) => {
        console.log(`  获取私聊文件URL: ${fid}`)
        return 'http://example.com/private_file.json'
      }
    }
  }
  
  try {
    const fileUrl = await privateFileData.friend.getFileUrl(privateFileData.file.fid)
    if (fileUrl) {
      const res = await fetch(fileUrl)
      const arrayBuffer = await res.arrayBuffer()
      const buffer = Buffer.from(arrayBuffer)
      const base64 = buffer.toString('base64')
      const name = privateFileData.file.name
      
      console.log(`  ✓ 私聊文件处理成功: ${name}, base64长度: ${base64.length}`)
    }
  } catch (error) {
    console.log(`  ✗ 私聊文件处理失败: ${error.message}`)
  }
  
  // 测试3: 文件发送到群聊和私聊
  console.log('\n3. 测试文件发送:')
  
  // 模拟Bot
  global.Bot = {
    pickGroup: (id) => ({
      fs: {
        upload: async (buffer, path, filename) => {
          console.log(`  ✓ 群聊文件上传成功: 群${id}, 文件${filename}, 大小${buffer.length}bytes`)
          return true
        }
      }
    })
  }
  
  const testFileData = 'test.json|eyJ0ZXN0IjoiZGF0YSJ9'
  const fileParts = testFileData.split('|')
  
  if (fileParts.length >= 2) {
    const fileName = fileParts[0]
    const base64Data = fileParts[1]
    const buffer = Buffer.from(base64Data, 'base64')
    
    // 测试群聊上传
    console.log('  测试群聊文件上传:')
    try {
      await global.Bot.pickGroup('123456').fs.upload(buffer, '/', fileName)
    } catch (error) {
      console.log(`    ✗ 群聊上传失败: ${error.message}`)
    }
    
    // 测试私聊发送
    console.log('  测试私聊文件发送:')
    const fileSegment = segment.file(buffer, fileName)
    console.log(`  ✓ 私聊文件segment创建成功: ${JSON.stringify(fileSegment)}`)
  }
}

// 运行所有测试
async function runAllTests() {
  testDuplicateMessageLogic()
  await testFileProcessing()
  console.log('\n=== 所有测试完成 ===')
}

runAllTests().catch(console.error)
