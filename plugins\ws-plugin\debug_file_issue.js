// 调试文件发送到gsuidcore的问题

// 模拟全局变量
global.Bot = {}
global.logger = {
  info: (...args) => console.log('[INFO]', ...args),
  warn: (...args) => console.log('[WARN]', ...args),
  error: (...args) => console.log('[ERROR]', ...args),
  mark: (...args) => console.log('[MARK]', ...args)
}

global.segment = {
  file: (buffer, name) => ({ type: 'file', buffer, name })
}

global.Version = { isTrss: false }
global.Config = { noMsgInclude: [] }

// 模拟可能导致文件处理失败的情况
async function testFileProcessingFailures() {
  console.log('=== 调试文件处理失败的情况 ===')
  
  // 模拟makeGSUidReportMsg函数
  async function makeGSUidReportMsg(e, botId = 'onebot') {
    let message = []
    let msg = e.message
    
    console.log(`开始处理消息，包含 ${msg.length} 个元素`)
    
    if (e.source) {
      message.push({
        type: 'reply',
        data: String(e.source.message_id)
      })
    }
    
    for (const i of msg) {
      console.log(`处理消息元素: ${i.type}`)
      switch (i.type) {
        case 'text':
          message.push({
            type: 'text',
            data: i.text
          })
          console.log(`  ✓ 文本消息添加成功: ${i.text}`)
          break
        case 'file':
          console.log(`  开始处理文件: ${i.name}`)
          try {
            let fileUrl
            if (e.isGroup) {
              console.log(`    群文件处理`)
              fileUrl = await e.group?.getFileUrl?.(e.file.fid)
            } else {
              console.log(`    私聊文件处理`)
              fileUrl = await e.friend?.getFileUrl?.(e.file.fid)
            }
            
            console.log(`    获取到文件URL: ${fileUrl}`)
            
            if (fileUrl) {
              // 模拟fetch可能失败的情况
              if (fileUrl.includes('fail')) {
                throw new Error('模拟fetch失败')
              }
              
              let res = await fetch(fileUrl)
              let arrayBuffer = await res.arrayBuffer()
              let buffer = Buffer.from(arrayBuffer)
              let base64 = buffer.toString('base64')
              let name = i.name
              
              message.push({
                type: 'file',
                data: `${name}|${base64}`
              })
              console.log(`  ✓ 文件处理成功: ${name}, base64长度: ${base64.length}`)
            } else {
              console.log(`  ✗ 无法获取文件URL`)
            }
          } catch (error) {
            console.log(`  ✗ 文件处理失败: ${error.message}`)
          }
          break
        default:
          break
      }
    }
    
    console.log(`处理完成，message数组长度: ${message.length}`)
    
    if (message.length == 0) {
      console.log('❌ message为空，返回false')
      return false
    }
    
    const MessageReceive = {
      bot_id: botId,
      bot_self_id: String(e.self_id),
      msg_id: String(e.message_id),
      user_id: String(e.user_id),
      user_pm: 6,
      content: message,
      sender: {
        ...e.sender,
        user_id: String(e.user_id)
      }
    }
    
    if (e.isGroup) {
      MessageReceive.user_type = 'group'
      MessageReceive.group_id = String(e.group_id)
    } else {
      MessageReceive.user_type = 'direct'
    }
    
    console.log('✅ 消息构建成功，准备发送到gsuidcore')
    return Buffer.from(JSON.stringify(MessageReceive))
  }
  
  // 模拟fetch
  global.fetch = async (url) => {
    console.log(`    fetch请求: ${url}`)
    if (url.includes('fail')) {
      throw new Error('网络请求失败')
    }
    const testData = JSON.stringify({ test: 'file_data' })
    return {
      arrayBuffer: async () => new TextEncoder().encode(testData).buffer
    }
  }
  
  // 测试1: 只有文件的消息，文件处理成功
  console.log('\n1. 测试只有文件的消息（文件处理成功）:')
  const successEvent = {
    message: [
      { type: 'file', name: 'success.txt' }
    ],
    isGroup: false,
    file: { fid: 'success123' },
    friend: {
      getFileUrl: async (fid) => {
        console.log(`    获取文件URL: ${fid}`)
        return 'http://example.com/success.txt'
      }
    },
    self_id: 12345,
    message_id: 67890,
    user_id: 11111,
    sender: { nickname: 'TestUser' }
  }
  
  const successResult = await makeGSUidReportMsg(successEvent)
  console.log(`结果: ${successResult ? '成功' : '失败'}`)
  
  // 测试2: 只有文件的消息，文件处理失败
  console.log('\n2. 测试只有文件的消息（文件处理失败）:')
  const failEvent = {
    message: [
      { type: 'file', name: 'fail.txt' }
    ],
    isGroup: false,
    file: { fid: 'fail123' },
    friend: {
      getFileUrl: async (fid) => {
        console.log(`    获取文件URL: ${fid}`)
        return 'http://example.com/fail.txt'  // 包含'fail'会触发错误
      }
    },
    self_id: 12345,
    message_id: 67891,
    user_id: 11111,
    sender: { nickname: 'TestUser' }
  }
  
  const failResult = await makeGSUidReportMsg(failEvent)
  console.log(`结果: ${failResult ? '成功' : '失败'}`)
  
  // 测试3: 文件+文本消息，文件处理失败
  console.log('\n3. 测试文件+文本消息（文件处理失败）:')
  const mixedEvent = {
    message: [
      { type: 'text', text: '这是一个文件:' },
      { type: 'file', name: 'fail.txt' }
    ],
    isGroup: false,
    file: { fid: 'fail123' },
    friend: {
      getFileUrl: async (fid) => {
        console.log(`    获取文件URL: ${fid}`)
        return 'http://example.com/fail.txt'  // 包含'fail'会触发错误
      }
    },
    self_id: 12345,
    message_id: 67892,
    user_id: 11111,
    sender: { nickname: 'TestUser' }
  }
  
  const mixedResult = await makeGSUidReportMsg(mixedEvent)
  console.log(`结果: ${mixedResult ? '成功' : '失败'}`)
  
  if (mixedResult) {
    const parsed = JSON.parse(mixedResult.toString())
    console.log(`消息内容: ${parsed.content.map(c => c.type).join(', ')}`)
  }
  
  // 测试4: 无法获取文件URL的情况
  console.log('\n4. 测试无法获取文件URL的情况:')
  const noUrlEvent = {
    message: [
      { type: 'file', name: 'nourl.txt' }
    ],
    isGroup: false,
    file: { fid: 'nourl123' },
    friend: {
      getFileUrl: async (fid) => {
        console.log(`    尝试获取文件URL: ${fid}`)
        return null  // 返回null模拟无法获取URL
      }
    },
    self_id: 12345,
    message_id: 67893,
    user_id: 11111,
    sender: { nickname: 'TestUser' }
  }
  
  const noUrlResult = await makeGSUidReportMsg(noUrlEvent)
  console.log(`结果: ${noUrlResult ? '成功' : '失败'}`)
}

// 运行测试
async function runDebugTests() {
  await testFileProcessingFailures()
  
  console.log('\n=== 调试总结 ===')
  console.log('如果测试2和测试4显示"失败"，说明文件处理失败时整个消息都不会发送到gsuidcore')
  console.log('这可能是导致"现在没有发送文件到gsuidcore了"的原因')
  console.log('解决方案：即使文件处理失败，也应该发送其他部分的消息')
}

runDebugTests().catch(console.error)
